<?php
// Auto-generated blog post
// Source: encyclical-environment.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Pope <PERSON> Climate Talking Points
"Date:": September 28, 2015';
$meta_description = 'I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the <PERSON>. Here are some boiled down ideas from their Encyclical on the Environment we hunger striked over.';
$meta_keywords = 'climate, masscommunication, homeless, advocacy, CompassionateCities, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Pope Francis Climate Talking Points
"Date:": September 28, 2015',
  'tags' => 
  array (
    0 => 'climate',
    1 => 'masscommunication',
    2 => 'homeless',
    3 => 'advocacy',
    4 => 'CompassionateCities',
  ),
  'excerpt' => 'I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the Pope. Here are some boiled down ideas from their Encyclical on the Environment we hunger striked over.',
  'categories' => 
  array (
    0 => 'Journal',
    1 => 'Climate',
    2 => 'Inspiration',
    3 => 'Personal Stories',
  ),
  'author' => 'A. A. Chips',
  'source_file' => 'content\\climate\\encyclical-environment.md',
);

// Raw content
$post_content = '<p><a href="http://localhost/" class="external-link">A. A. Chips</a></p>

<ul><li><a href="http://localhost/" class="external-link">Home</a></li>
<p><li><a href="http://localhost/about" class="external-link">About</a></li></p>
<p><li><a href="http://localhost/advocacy" class="external-link">Advocacy</a></li></p>
<p><li><a href="http://localhost/personal" class="external-link">Personal</a></li></p>
<p><li><a href="http://localhost/gallery" class="external-link">Gallery</a></li></p>

<h2>Pope Francis Climate Talking Points</h2>

<p>On September 24th, 2015, I got to see Pope Francis speak on their Encyclical on the Environment in Washington D.C. I was part of a group that in the eighteen days before, was <a href="https://archive.org/details/FERCFastBrokenAndMarch9252015540p" target="_blank">engaged in a hunger strike</a>. I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the Pope. They had been given those tickets by their work with the Energy Department, and couldn\'t make it. The lines were opening at 6:00 AM and the night before, many of us camped outside by the Meade Memorial, staying up all night in the cold singing songs and drinking tea. By the time we were lining up, even with that early set arrival, Pope Francis was a dot on the horizon when they gave their speech.</p>

<p>This visit to the United States was to three cities, New York, Washington DC, and Chicago. This was to deliver their message, their encyclical on the environment. <a href="https://www.vatican.va/content/francesco/en/encyclicals/documents/papa-francesco_20150524_enciclica-laudato-si.html" target="_blank">Laudato Si</a>, or \'On Care for our Common Home,\' criticizes consumerism, irresponsible economic development, environmental degradation, and global warming. This meme boils down the document into seven talking points.</p>

<img src="../img/popeFrancisClimatePoints.jpg" alt="popeFrancisClimatePoints.jpg">

<p><em>Any harm done to the environment is harm done to humanity. The climate is a common good, belonging to all, and meant for all.</em></p>

<p><em>We are not faced with two separate crises, one environmental and the other social. But one crisis which is both social and environmental. The poor and earth are crying out</em></p>

<p><em>What is at stake is our own dignity. Climate change dramatically affects us, for it has to do with the ultimate meaning of our earthly sojourn.</em></p>

<p><em>We are one single human family. We have a shared responsibility for our common home. What kind of world do we want to leave to children who are now growing up?</em></p>

<p><em>Now is the time for an integrated approach to combating poverty, restoring dignity to the excluded, and at the same time protecting nature.</em></p>

<p><em>There is an urgent need for policies so that drastically reduce coal, oil, and gas, and replace them with renewable energy.</em></p>

<p><em>I am convinced we can make a difference. I am confident we can find a solution.</em></p>

<p><em>I call for a courageous effort to redirect our steps.</em></p>


<p>https://www.vaticannews.va/en/pope/news/2023-10/laudate-deum-pope-francis-climate-crisis-laudato-si.html</p>

<h3>About A. A. Chips</h3>

<p>Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.</p>

<h2>Donate</h2>

<p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="https://www.ko-fi.com/aachips" class="external-link">Ko-Fi page</a>.</p>

<p><a href="crowdfunding-campaign-a-a-chips-1.php" class="internal-link">What am I raising funds for?</a></p>

<h3>Categories</h3>


<p><li><a href="http://localhost/advocacy" class="external-link">Advocacy</a></li></p>
<p><li><a href="/kitchen" class="internal-link">Apple Chip Kitchen</a></li></p>
<p><li><a href="/alienation" class="internal-link">Alienation</a></li></p>
<p><li><a href="/climate" class="internal-link">Climate</a></li></p>
<p><li><a href="/humor" class="internal-link">Humor</a></li></p>
<p><li><a href="/inspire" class="internal-link">Inspiration</a></li></p>
<p><li><a href="/journal" class="internal-link">Journal</a></li></p>
<p><li><a href="/maze" class="internal-link">Maze of Wonders</a></li></p>
<p><li><a href="http://localhost/personal" class="external-link">Personal Stories</a></li></p>
<p><li><a href="/writings" class="internal-link">Writings</a></li></ul></p>

<h3>Connect</h3>

<p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email.</p>


<p>© 2025 A. A. Chips. All rights reserved.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>