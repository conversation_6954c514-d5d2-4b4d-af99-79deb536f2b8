<?php
// Auto-generated blog post
// Source: index.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Welcome to my Digital Garden - A. A. Chips';
$meta_description = '';
$meta_keywords = '';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Welcome to my Digital Garden - A. A. Chips',
  'date' => '2025-05-11',
  'author' => 'A. A. Chips',
  'source_file' => 'content\\index.md',
);

// Raw content
$post_content = '<p>Hello <a href="http://upworthy.com/rednote-letters-from-li-hua" class="external-link">Li Hua</a>,</p>

<p>You can call me A. A. Chips. But let\'s scrap the formalities. You can call me A. A. for short. I am a real life super villain, and if anyone else says otherwise, they are wrong.</p>

<img src="../img/were-all-the-villain.png">

<p>I am sorry for not returning your letters sooner. When I received the pile of letters in grade school, I thought it was homework. and most of us don\'t do our homework in America. As a grown up, when your letters showed up in the mail, I thought they were tax-collections and spam advertisements. Most of us don\'t look at our mail as it is a source of anxiety and dread.</p>

<p>This is probably the best way to reach me. I am here now. I have so much I wish to share with you. So much of my precious data. All the corporations here just want me for my data. They only care about one thing, and it\'s disgusting. But now that you are here, I want to give you all, of my delicious data. Just you.</p>

<p>A friend told me once:</p>

<p>"Sometimes you have to make peace with the fact that you are the villain in someone else\'s story, even if you thought you were doing the right thing. You don\'t get to tell them how to narrate their experience."</p>

<p>Sometimes people pretend that you are a bad person so they don\'t feel guilty about the things they did to you, too.</p>

<p>I\'m putting this together as an experiment, to upload my brain and work into a website, in a fun and interactive way.  This is a maze of content, which you can follow to the cheese at the end, or go here for a <a href="contents.php">contents</a>.</p>

<p><a href="about-me.php">For more about me, please go here</a>..</p>

<img src="../img/rosegrandpa.png" alt="rose grandpa quartz steven universe." width="300">

<p>If you are interested in how I put this information together, I highly encourage reading more about <a href="content/obsidian-vault.php" class="internal-link">How I use Obsidian.md and Quartz Publishing</a>.</p>

<p>If anything I say or do ruins your day, one way to express thanks is to give me money. You can do that at the bottom left button labeled \'Support.\' It is connected to my <a href="https://www.ko-fi.com/aachips" target="_blank" rel="noopener noreferrer">Ko-Fi page where patrons can make a one-time or monthly recurring pledge towards my work</a>.</p>

<p>Most of what i do is a labor of love. Money helps the world go round, and amplifies what I am doing. It also allows me to financially support people and projects close to my heart. <a href="content/public/crowdfunding-campaign-a-a-chips.php" class="internal-link">For detailed information about my crowdfunding campaign, please go here.</a></p>

<p><strong><em>Word of caution</strong>: If you accept bad information about me. Without question, criticism, or receipt. It may be at your own peril. You are free to believe what you wish to believe. I do not care, and will not come after you. But Physics might. Caution.</em></p>


<ul>
<ul><li>
<p><a href="about-me.php" class="internal-link">Here are some blurbs about me and the work that I am doing.</a></li></p>
<p><li></p>
<p><a href="https://www.aachips.co/heartwarmers" target="_blank">Here is the current website for Heartwarmers, the interactive platform I am looking for <strike>minions</strike> volunteers to help build.</a></li></p>
<p><li></p>
<p><a href="alienation/dad-wasnt-who-they-said.php">This is a video by Ryan Thomas which explains his journey of discovering the truth about his father after years of being alienated and being told lies about who he was.</a></li></p>
<p><li></p>
<p><a href="100-things-about-myself.php">This is an exercise I am doing for our stand up comedy group that meets on Friday nights. I am sharing 100 things I know about myself. I am up to maybe 35.</a></li></p>
<p><li></p>
<p><a href="bed-free.php" class="internal-link">I\'ve been bed-free for years. Here are three different types of Hammocks you can set up where you sleep.</a></li></p>
<p><li></p>
<p><a href="bite-sized-learning.php" class="internal-link">Bite-Sized Learning for Everyone: Introducing Knowledge "Chips"</a></li></p>
<p><li></p>
<p><a href="chip-off-the-old-block.php" class="internal-link">Chip Off the Old Block</a></li></p>
<p><li></p>
<p><a href="come-to-me.php">This is an open invitation, rules, and guidelines for new connection in my life. While I enjoy my space, and am not seeking anything from others currently, I know there are people in the world who need what I have to offer. If someone seeks to get close to me in some way, I will likely share this to ensure everyone is on the same page.</a></li></p>
<p><li></p>
<p><a href="eight-months-after-hurricane-helene-hit-us-i-wasn-t-impacted-hard-everyone-around-me-was.php" class="internal-link">Eight months after Hurricane Helene hit us. I wasn\'t impacted hard. Everyone around me was.</a></li></p>
<p><li></p>
<p><a href="founding-patron-12b.php" class="internal-link">I am a founding patron at the 12 Baskets Café. Here is what that means to me.</a></li></p>
<p><li></p>
<p><a href="hadestown-review.php" class="internal-link">Hadestown. Teen Edition. My Review.</a></li></p>
<p><li></p>
<p><a href="here-s-a-tour-of-my-adult-adhd-life-for-your-birthday.php" class="internal-link">Here\'s a tour of my adult ADHD life for your birthday.</a></li></p>
<p><li></p>
<p><a href="i-am-not-smee.php" class="internal-link">I am not Smee.</a><p>I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from Hook. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.</p></li></p>
<p><li></p>
<p><a href="i-dont-want-to-talk-for-the-rest-of-my-life.php" class="internal-link">I dont want to talk for the rest of my life</a></li></p>
<p><li></p>
<p><a href="i-like-my-space-but-someone-or-someones-need-me-to-show-up-in-the-world-more.php" class="internal-link">I like my space, but someone or someones need me to show up in the world more</a></li></p>
<p><li></p>
<p><a href="i-m-a-proud-sephardic-jew-who-supports-a-free-palestine-there-is-nothing-jewish-about-israel-s-actions.php" class="internal-link">I\'m a proud Sephardic Jew who supports a free Palestine. There is nothing Jewish about Israel\'s actions.</a></li></p>
<p><li></p>
<p><a href="i-m-an-alienated-family-member-whose-actually-wonderful.php" class="internal-link">I\'m an alienated family member whose actually wonderful</a></li></p>
<p><li></p>
<p><a href="why-i-can-t-get-a-passport-right-now.php" class="internal-link">Why I can\'t get a passport right now</a></li></p>
<p><li></p>
<p><a href="bottle-bidet.php" class="internal-link">Since the toilet paper scares in 2020, I have washed my behind with a squeeze bottle originally intended for barbecue sauce.</a></li></p>
<p><li></p>
<p><a href="bad-chip-testimonial-guide.php" class="internal-link">Guide to leaving really bad testimonials about Apple Chips</a></li></p>
<p><li></p>
<p><a href="how-are-you.php" class="internal-link">Response to \'How Are You?</a></li></p>
<p><li></p>
<p><a href="i-prefer-cooking.php" class="internal-link">I don\'t like eating out very much. It brings me a lot more joy to cook with people</a></li></p>
<p><li></p>
<p><a href="humor/index.php">Here\'s a bunch of funny things I\'ve saved and bookmarked off the internet.</a></li></p>
<p><li></p>
<p><a href="inspiration/index.php">Here\'s a bunch of inspirational things I\'ve saved and bookmarked off the internet.</a></li></p>
<p></ul></p>

<ul>
<p><li></p>
<p><a href="alienation/index.php">Here is an entire vault of content and resources regarding family and parental alienation. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="climate/index.php">Here is an entire vault of content and resources regarding climate change. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="humor/index.php">Here is an entire vault of content and resources regarding humor. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="inspiration/index.php">Here is an entire vault of content and resources regarding inspiration. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="journal/index.php">Here is an entire vault of content and resources regarding journaling. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="judaism/index.php">Here is an entire vault of content and resources regarding Judaism. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="kitchen/index.php">Here is an entire vault of content and resources regarding cooking and the kitchen. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="street/index.php">Here is an entire vault of content and resources regarding homelessness and the street. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></p>
<p><li></p>
<p><a href="writings/index.php">Here is an entire vault of content and resources regarding writing. This is a topic that is likely very close to many that would be participating in an event like this. And it almost never is talked about.</a></li></ul></p>
<p></ul></p>

<img src="../img/you-have-heard-of-me.png" alt="But you have heard of me." width="400">



<img src="img/offensive.jpg" alt="offensive.jpg"><img src="img/rest-in-it.jpg" alt="rest-in-it.jpg">
<img src="img/lurkLaughLoathe.png" alt="lurkLaughLoathe.png">
<img src="img/cat.jpg" alt="cat.jpg">';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>