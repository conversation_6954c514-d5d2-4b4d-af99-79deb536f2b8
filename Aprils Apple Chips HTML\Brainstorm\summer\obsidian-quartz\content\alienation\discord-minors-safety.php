<?php
// Auto-generated blog post
// Source: discord-minors-safety.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Connecting on Discord and Safety Tips for Minors';
$meta_description = 'Guidelines and safety practices for young people using Discord and other online platforms';
$meta_keywords = 'online-safety, discord, youth, resources, draft, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Connecting on Discord and Safety Tips for Minors',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'online-safety',
    1 => 'discord',
    2 => 'youth',
    3 => 'resources',
    4 => 'draft',
  ),
  'excerpt' => 'Guidelines and safety practices for young people using Discord and other online platforms',
  'source_file' => 'content\\alienation\\discord-minors-safety.md',
);

// Raw content
$post_content = '<h1>Connecting on Discord and Safety Tips for Minors</h1>

<h2>Reflection Questions</h2>
<ul><li>What are the most important safety considerations for young people in online spaces?</li>
<p><li>How can we balance connection and community with appropriate boundaries and safety?</li></p>
<p><li>What personal experiences have informed your understanding of online safety?</li></p>
<p><li>How can parents and young people communicate effectively about online interactions?</li></p>

<h2>Initial Thoughts</h2>
<p>Discord and other online platforms offer valuable opportunities for connection, but also present unique safety challenges for young users. This resource can provide:</p>
<p><li>Age-appropriate safety guidelines for Discord and similar platforms</li></p>
<p><li>Warning signs of potential predatory behavior</li></p>
<p><li>Privacy settings and account security best practices</li></p>
<p><li>Healthy boundaries for online interactions</li></p>
<p><li>Resources for parents and young people</li></p>

<h2>Safety Guidelines</h2>
<p>[Space for specific safety recommendations for Discord and other platforms]</p>

<h2>Privacy and Security</h2>
<p>[Space for practical tips on privacy settings, account security, and information sharing]</p>

<h2>Recognizing Red Flags</h2>
<p>[Space for discussing warning signs of inappropriate behavior or manipulation]</p>

<h2>Resources and Support</h2>
<p>[Space for listing helpful resources, reporting mechanisms, and support options]</p>

<h2>Related Content</h2>
<p><li><a href="discordsafety-png.php" class="internal-link">discordSafety.png</a></li></p>
<p><li><a href="cultivating-community-and-solidarity.php" class="internal-link">Cultivating Community and Solidarity</a></li></p>
<p><li><a href="rethinking-digital-ecosystems-a-call-for-ecological-literacy-in-tech.php" class="internal-link">Rethinking Digital Ecosystems - A Call for Ecological Literacy in Tech</a></li></ul></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>