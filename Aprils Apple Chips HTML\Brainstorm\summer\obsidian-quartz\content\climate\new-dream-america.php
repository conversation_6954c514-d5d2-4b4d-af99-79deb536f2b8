<?php
// Auto-generated blog post
// Source: new-dream-america.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Marchers for climate change have a new dream for America - Zuni';
$meta_description = 'The last of the marchers appeared on the horizon, defying the odds. The young man was not only walking with crutches - he was barefoot. His female companion kept his pace and communicated with him with her hands. She could not talk.';
$meta_keywords = 'climate, march, history, journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Marchers for climate change have a new dream for America - Zuni',
  'date' => '2014-05-08',
  'tags' => 
  array (
    0 => 'climate',
    1 => 'march',
    2 => 'history',
    3 => 'journal',
  ),
  'author' => 'Vida Volkert',
  'excerpt' => 'The last of the marchers appeared on the horizon, defying the odds. The young man was not only walking with crutches - he was barefoot. His female companion kept his pace and communicated with him with her hands. She could not talk.',
  'source_file' => 'content\\climate\\new-dream-america.md',
);

// Raw content
$post_content = '<p>Marchers for climate change have a new dream for America</p>

<p>=================================================</p>

<p>Article appeared in the print version of the Gallup Independent, May 8, 2014</p>

<p>By Vida Volkert</p>

<p><NAME_EMAIL></p>

<p>ZUNI - The last of the marchers appeared on the horizon, defying the odds. The young man was not only walking with crutches - he was barefoot.</p>

<p>His female companion kept his pace and communicated with him with her hands. She could not talk.</p>

<p>They were sunburned, sweaty and tired, yet they were smiling and cheering each other as they walked along New Mexico Highway 53.</p>

<p>It was a strange, but inspiring scene that humbled Zuni Detention Center corrections administrator Tyler Lastiano. At the center, the rest of the marchers were already setting up camp on a recent afternoon.</p>

<p>"My first impression was, \'Oh they have been walking for a long time,\'" Lastiano said.</p>

<p>"It was amazing. They have conquered the elements so far. It\'s been hot. It\'s been windy. It\'s been raining. And they continue their journey."</p>

<p>The group of about 30 marchers led by Ed Fallon, founder and director of the Great March for Climate Action, started out of Los Angeles March 1. They have been walking or riding bicycles about 15-20 miles a day, while spending the night along the road. They are scheduled to arrive in Washington Nov. 1.</p>

<p>"I love this planet. It\'s our only home, and I\'ll do what it takes to protect it," Mackenzie McDonald Wilkins, 24, said. He injured his right foot during the march and decided to continue on crutches.</p>

<p>"It\'s a spiritual journey for me. I\'m learning that pretty much everything I knew has been challenged. My assumptions about my place in the world, as an individual, have been completely broken down."</p>

<p>As he explained his reasons to march, his companion Sean Glenn, 22, nodded and pointed at the sky, the rock formations and the juniper trees along the road.</p>

<p>McDonald Wilkins explained that Glenn took a vow of silence in an attempt to better connect with nature during the march.</p>

<p>The marchers are not only trying to raise awareness about climate change, McDonald Wilkins said. They believe American culture encourages individuality, independence and isolation; and that\'s leading to spiritual emptiness.</p>

<p>He said taking action to protect the planet means building sustainable communities that encourage cooperation instead of competition, which ultimately leads to a richer life. Through the march he has learned to depend on members of his group and on the good will, disposition and kindness of strangers.</p>

<p>"It\'s really about the interactions amongst us, how you bring a group together, how we interact with the community," he said. "There\'s no reason to be fatalistic. I\'m very optimistic. Human history has been leading up to this moment. We gotta stop getting away from our connection to this planet and stop creating systems of exploitation."</p>

<p>At Zuni the group was given a tour. They visited Ashiwi Awan Museum, where the learned about the Zuni myth of emergence from the Grand Canyon and migration to present day Zuni. They attended a religious dance at the plaza and were also invited to present about their cause to the Zuni government.</p>

<p>They had been on the road for several days. We open our facility so they could use the bathrooms and have access to showers," Lastiano said. "They were asking for a laundromat, but Zuni does not have a laundromat. We offered to do their laundry. We let them use our commercial facility."</p>

<p>The group continued the march the next day with lunch bags made by tribal members. They spent a night camping outside El Morro\'s Ancient Way Cafe, another night in the Zuni Mountains, and in Grants.</p>

<p>"We are doing this march as environmentally as possible," Pablo Howard, who drove the "break truck" said. "We are pulling our toilet with us."</p>

<p>The vehicle included a portable toilet and a solar powered station, where marchers could charge cellphones, cameras and laptops - in all, 22 devices at one time. The vehicle also included kitchen equipment and supplies.</p>

<p>Howard said he began paying attention to power and climate issues in 1969, when he was stationed in Germany with the U.S. Army. When he was asked why Americans were fighting in Vietnam, he said, "I didn\'t know the answer."</p>

<p>\'I think climate change is one of the most important crisis of this era," Howard said.</p>

<p>"Western civilization needs to change its dream - we are spiritually devoid. You set a goal of an environmentally sustainable society. A society of social justice in cooperation, not competition, that involves non-violence, peace. Don\'t bother me with \'it has never been done.\' That it hasn\'t, doesn\'t mean it can\'t be done."</p>

<p>Information or donations: <http://climatemarch.org/></p>

<p>Article courtesy of the Gallup Independent, May 8, 2014</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>