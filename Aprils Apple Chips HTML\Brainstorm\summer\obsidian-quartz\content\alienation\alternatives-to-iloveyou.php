<?php
// Auto-generated blog post
// Source: alternatives-to-iloveyou.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Alternatives to \'I love you\'';
$meta_description = 'How to show you care without saying \'I love you\'';
$meta_keywords = 'memes, alienation, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'author' => 'Sue Ellson',
  'title' => 'Alternatives to \'I love you\'',
  'topic' => 'Personal growth',
  'excerpt' => 'How to show you care without saying \'I love you\'',
  'keywords' => 'love, care, family, friends, partners, communication',
  'tags' => 
  array (
    0 => 'memes',
    1 => 'alienation',
  ),
  'source_file' => 'content\\alienation\\alternatives-to-iloveyou.md',
);

// Raw content
$post_content = '<p>“The most important thing in this world is to learn to give out love and let it come in.” ~Morrie Schwartz</p>

<p>As a child, I never heard the phrase “I love you.” Now, I hear people say it all the time—at the end of phone calls and whenever parting ways.</p>

<p>When I moved away from my hometown of Adelaide, South Australia, twenty years ago, I noticed how much less I felt loved interstate in Melbourne, Victoria. Even though I didn’t hear “I love you” when I was in Adelaide, somehow I knew people cared.</p>

<p>Soon after I arrived here, I had two wonderful children who’ve taught me all about love. They regularly tell me they love me, and I often overhear them telling their friends.</p>

<p>This got me thinking: how can we <a href="http://tinybuddha.com/blog/50-ways-to-show-you-care-without-spending-a-dime/" class="external-link">let people know we care</a>, beyond simply saying “I love you?”</p>

<p>I decided to make a list of some expressions that we can all say more often to family, friends, partners, and even colleagues. Perhaps you could use one of these each week for the next year.</p>

<ul><li>You are special to me.</li>
<p><li>I feel amazing when I spend time with you.</li></p>
<p><li>You give me goosebumps.</li></p>
<p><li>I feel safe sharing my secrets with you.</li></p>
<p><li>I accept you as you are.</li></p>
<p><li>I understand how you feel.</li></p>
<p><li>Is there anything I can do to help?</li></p>
<p><li>I always have fun when I am with you.</li></p>
<p><li>Please tell me how it is for you so I can understand.</li></p>
<p><li>Can I hold your hand?</li></p>
<p><li>Can I give you a hug?</li></p>
<p><li>You inspire me.</li></p>
<p><li>I really appreciate it when you…</li></p>
<p><li>You are one of the most amazing gifts I have ever received.</li></p>
<p><li>I value everything you’ve taught me.</li></p>
<p><li>The insights you have shared mean the world to me.</li></p>
<p><li>Your thoughtfulness is a delight to receive.</li></p>
<p><li>I will never forget how you…</li></p>
<p><li>I feel so relaxed and happy when you…</li></p>
<p><li>Seeing you when … happened made it all okay.</li></p>
<p><li>I can feel it when your heart sings because it makes my heart sing too.</li></p>
<p><li>I could sit next to you and not say anything and be at peace.</li></p>
<p><li>The way you handled … showed me that you are truly…</li></p>
<p><li>Your comments about … helped me enormously.</li></p>
<p><li>I’m thankful to have you in my life.</li></p>
<p><li>I could go anywhere with you.</li></p>
<p><li>I believe your intentions for me are always good, even when I cannot understand what you do.</li></p>
<p><li>I trust you.</li></p>
<p><li>I can go outside of my comfort zone with you.</li></p>
<p><li>Knowing you gives me courage.</li></p>
<p><li>The world is less scary when I am with you.</li></p>
<p><li>I appreciate that your suggestions help me make difficult choices.</li></p>
<p><li>I lose all concept of time when I am with you.</li></p>
<p><li>If something serious happened to me, you’re the first person I would call.</li></p>
<p><li>You are so generous in spirit.</li></p>
<p><li>Surprise me more often because I like your surprises.</li></p>
<p><li>I love how you … whenever I need to …</li></p>
<p><li>I hear your voice even when we are not in the same place.</li></p>
<p><li>I feel connected to you even when I cannot see you.</li></p>
<p><li>Your wisdom has saved me.</li></p>
<p><li>I feel refreshed and renewed around you.</li></p>
<p><li>I enjoy your sense of humor.</li></p>
<p><li>Whenever I see a photo of us together, I smile.</li></p>
<p><li>I appreciate that you think about my feelings before you do and say things.</li></p>
<p><li>Your smile makes me smile.</li></p>
<p><li>I love that you know me so well.</li></p>
<p><li>When I think about you, I often remember when you…</li></p>
<p><li>I want to keep you in my past, present, and future.</li></p>
<p><li>I can be me when I am with you—I hope you feel the same way.</li></p>
<p><li>Circumstance brought us together; choice keeps us together.</li></p>
<p><li>You are so lovable.</li></p>
<p><li>I love you.</li></ul></p>

<p>I know that the positive feedback I’ve received in the past has kept me going during the darkest moments of my life.</p>

<p>I hope that by saying “I love you” in many different ways, the special people in your life will have good memories that can sustain them during the more difficult moments in their lives.</p>

<p>How do you let people know you love them?</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>