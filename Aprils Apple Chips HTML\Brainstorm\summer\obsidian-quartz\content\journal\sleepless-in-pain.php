<?php
// Auto-generated blog post
// Source: sleepless-in-pain.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Sleepless Pain and Overthinking in Bed Late - August 2017';
$meta_description = 'It\'s 1:30 in the morning and I can\'t sleep. <PERSON> is hosting me and I\'m sleeping in the guest room. Currently I am on my trip to Maryland. It\'s too hot for the car. This heavy weight is keeping me awake, bringing me tears and a familiar backache.';
$meta_keywords = 'journal, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Sleepless Pain and Overthinking in Bed Late - August 2017',
  'date' => '2017-08-11',
  'author' => 'A. A. Chips',
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'tags' => 
  array (
    0 => 'journal',
    1 => 'homeless',
  ),
  'excerpt' => 'It\'s 1:30 in the morning and I can\'t sleep. Susan is hosting me and I\'m sleeping in the guest room. Currently I am on my trip to Maryland. It\'s too hot for the car. This heavy weight is keeping me awake, bringing me tears and a familiar backache.',
  'source_file' => 'content\\journal\\sleepless-in-pain.md',
);

// Raw content
$post_content = '<p>It\'s 1:30 in the morning and I can\'t sleep. Susan is hosting me and I\'m sleeping in the guest room. Currently I am on my trip to Maryland. It\'s too hot for the car. This heavy weight is keeping me awake, bringing me tears and a familiar backache. The same one from mom\'s story last September at dinner. The one that left me with a really awful pit in my stomach since. I don\'t want to go to my sister\'s wedding right now. I don\'t want to go to my cousin\'s wedding in Scotland. Even though I always wanted to go to Scotland. I\'m longing for authenticity in my life, and the idea of wearing a tuxedo, and falling into my old family role pretending everything is alright is completely dreadful. I\'m still healing from my spine disc slipping last January, and I generally am sleeping 12+ hours a day. The lack of respect for boundaries while with them makes my back worse. I\'m not arguing with back pain. Self-preservation is more important than everything else right now.</p>

<p>Last month my relationship fell apart. I did my best at clear communication, advocating for my needs, especially for calmness when I\'m overwhelmed. I helped her with whatever she needed for her chronic pain, but when it came to quiet while I was overwhelmed, it was like I was asking for too much. Verbal attacks and beratement. I can\'t do it. no patience. No balance. Intimacy went from consensual to pressured and coerced. I didn\'t matter. My needs didn\'t matter. She would be an asshole while in pain, which was all the time with Fibromyalgia. It was incredibly controlling. I kept detailed records of communication and visit notes with her child. We agreed that I would gladly give her copies of these notes in exchange for participating in cognitive behavioral therapy like the judge ordered.</p>

<p>My mind is racing and nothing is quelling this pain. This is what happens when I sleep on a mattress.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>