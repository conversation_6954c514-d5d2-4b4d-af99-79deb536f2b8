<?php
// Auto-generated blog post
// Source: semitic-semantics.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'What Does It Mean to Be Semitic?  Semitic Semantics..';
$meta_description = 'Reclaiming Language, History, and Shared Humanity';
$meta_keywords = 'jewish, palestine, advocacy, writings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'What Does It Mean to Be Semitic?  Semitic Semantics..',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'jewish',
    1 => 'palestine',
    2 => 'advocacy',
    3 => 'writings',
  ),
  'excerpt' => 'Reclaiming Language, History, and Shared Humanity',
  'categories' => 
  array (
    0 => 'Judaism',
  ),
  'source_file' => 'content\\judaism\\semitic-semantics.md',
);

// Raw content
$post_content = '<h3>What Does It Mean to Be Semitic?  </h3>
<p>Reclaiming Language, History, and Shared Humanity<em></em></p>

<p>_For my Jewish cousins, my Arab siblings, and everyone caught in the crossfire of propaganda_</p>

<p>---</p>

<h3>Semitic Isn’t a Synonym for “Jewish”</h3>

<p>The word _Semitic_ refers to languages, not ethnicities or religions. It describes a linguistic family that includes:</p>

<ul><li><strong>Arabic</strong> (spoken by over 400 million people, from Morocco to Iraq).</li>
<p><li><strong>Hebrew</strong> (revived from near-extinction in the 19th century, with help from Arabic).</li></p>
<p><li><strong>Aramaic</strong> (the language of Jesus, now spoken by tiny diaspora communities).</li></p>

<p>Unless you know someone who went to high school with Jesus, you probably won’t meet an Aramaic speaker. But you’ll meet millions of Arabic speakers—many of whom share ancestors, foods, and cultural roots with Jewish people.</p>

<p><strong>Semitic ≠ Jewish.</strong> It’s a term co-opted to flatten history and justify division.</p>
<h3>Hebrew’s Revival: A Story of Linguistic Solidarity</h3>

<p>Hebrew was a _dead language_ for nearly 2,000 years, preserved only in religious texts. Its revival in the 19th and 20th centuries relied heavily on:</p>

<p><li><strong>Arabic grammar structures</strong> (modern Hebrew’s syntax mirrors Arabic).</li></p>
<p><li><strong>Loanwords</strong> (e.g., _sababa_—Hebrew slang for “cool”—comes from Arabic _ṣabāba_).</li></p>
<p><li><strong>Shared Levantine roots</strong> (both languages descend from the same ancient Semitic family).</li></p>

<p>This isn’t a footnote—it’s proof that our histories are intertwined. The same land that birthed Hebrew also birthed Arabic. The same soil.</p>

<h3>The Weaponization of “Anti-Semitism"</h3>

<p>Today, the term _anti-Semitism_ is often misused to:</p>

<p><li><strong>Silence critics of war crimes</strong> (as if opposing bombs = hating Jews).</li></p>
<p><li><strong>Erase Arab Semites</strong> (ignoring that Palestinians speak Arabic—a Semitic language).</li></p>
<p><li><strong>Divide natural allies</strong> (pitting Jewish and Arab people against each other).</li></p>

<p>But being _pro-Semitic_ means:</p>
<p>✔ <strong>Opposing wars over oil, power, and land</strong>—because violence destroys Semitic communities.</p>
<p>✔ <strong>Honoring shared roots</strong>—from Baghdad’s Jewish-Arab poets to the Mizrahi Jews expelled from Arab lands.</p>
<p>✔ <strong>Rejecting propaganda</strong> that tells us we’re enemies by design.</p>

<p>---</p>

<h3>For Jewish Readers Feeling Lost<em></em></h3>

<p>If you’re Jewish and heartbroken by what’s done in your name:</p>

<p><li><strong>You’re not alone.</strong> Many of us refuse to let our identity be a weapon.</li></p>
<p><li><strong>Your ancestry isn’t a monolith.</strong> Jewish voices have always included anti-war, anti-occupation, and pro-solidarity traditions.</li></p>
<p><li><strong>It’s okay to unlearn.</strong> The myth of “eternal conflict” is a political tool. Our great-grandparents often lived as neighbors.</li></p>

<h3>A Semitic Future</h3>

<p>Being Semitic isn’t about bloodlines—it’s about _connection_. It’s remembering that:</p>

<p><li>The Arabic _salaam_ and Hebrew _shalom_ both mean “peace.”</li></p>

<p><li>The hummus wars are silly; we all know the best recipes come from someone’s grandma.</li></p>

<p><li>Justice isn’t a zero-sum game. Safety for one people doesn’t require erasing another.</li></p>


<p><strong>Solidarity is our birthright.</strong> Let’s reclaim it.</p>

<p>---</p>

<h3><strong>Discussion Prompts (for Comments or Reflection)</strong></h3>

<p><li>What myths about “Semitic” identities were you taught?</li></p>

<p><li>How can we honor shared histories without erasing current struggles?</li></p>

<p><li>What does being _pro-Semitic_ look like in practice?</li></ul></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>