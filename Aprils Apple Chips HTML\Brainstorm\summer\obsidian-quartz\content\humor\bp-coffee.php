<?php
// Auto-generated blog post
// Source: bp-coffee.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'BP Spills Coffee: A Parody by UCB Comedy
"Date:": June 9, 2010';
$meta_description = '\'This is what happens when BP spills coffee.\'';
$meta_keywords = '#humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'BP Spills Coffee: A Parody by UCB Comedy
"Date:": June 9, 2010',
  'tags' => 
  array (
    0 => '#humor',
  ),
  'excerpt' => '\'This is what happens when BP spills coffee.\'',
  'source_file' => 'content\\humor\\bp-coffee.md',
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/2AAa0gd7ClM?si=k5uYJT6s01yw7jSW" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h1>BP Spills Coffee: a PARODY by UCB Comedy</h1>

<p><a href="https://www.youtube.com/@UCBComedy" class="external-link"><img src="https://yt3.ggpht.com/4zXWXpQMf4UeL4pNp-8JLNNm-jqHlS1Sa1O_ZISHAZ_QuaLrG6GdXEpTxlEwMP8RDWbrU6mm=s88-c-k-c0x00ffffff-no-rj" alt=""></a></p>

<p><a href="https://www.youtube.com/@UCBComedy" class="external-link">Upright Citizens Brigade</a></p>
<p>127K subscribers</p>
<p>13,714,682 views Jun 9, 2010</p>

<p>This is what happens when BP spills coffee. SUBSCRIBE:    <a href="http://www.youtube.com/user/UCBComedy" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/yt_favicon_ringo2.png" alt=""> / ucbcomedy</a>   If you liked this video, check out more videos from UCB Comedy:    <a href="https://www.youtube.com/watch?v=u3sK2O8I0hI&list=PLvMZxK22whIsWSSFONDd53FGZVBv-QtfU&index=1" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/yt_favicon_ringo2.png" alt=""> • Edward Snowden\'s Girlfriend is Hot: a...</a>   Director: Peter Schultz & Brandon Bassham Writers: Gavin Speiller, Eric Scott, Erik Tanouye, & John Frusciante Editor: Peter Schultz Starring: Eric Scott, Nat Freedberg, Kevin Cragg, Gavin Speiller, Kate McKinnon, John Frusciante, Zhubin Parang, Devlyn Corrigan, Erik Tanouye, Rob Lathan Producer: Todd Bieber Like UCB:   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbG9kZlBISXVrSjQ2bGZkMlJsSk50UEVWNVI1Z3xBQ3Jtc0tsdXQwQnV1SWswVTVlam1fRktaS0NaMG8zajRiNlcwOUhvbndvel9FMFEzWDhtNWxzSGl4VndwdTBHM3NMOWxVY242M1JYZDRXRmhpc0V3Vm12WEtFMDJWYWhOUDllak92WGFIT0FHOUdsaGt3TlJoNA&q=http%3A%2F%2Fwww.facebook.com%2FUCBcomedy&v=2AAa0gd7ClM" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/facebook_1x.png" alt=""> / ucbcomedy</a>   Follow UCB:   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbmhzWk1Geld6bDd4dExtZi0waG1hbjRZazZ5QXxBQ3Jtc0tsdkVjdTRDMjh2aEsxMnBjYXA0Tkg1elpnOWpNaUZKMGI1d05XZmpNZnVBdktkTnN5eGVRQkc1S1F5cHZWUm9aUGFRWE1reC15LTRGWEtRSGxlRWdYeVRVX2FzTUxYOEIxUXBRNktCX0E5TkhSdVRtcw&q=http%3A%2F%2Fwww.twitter.com%2FUCBcomedy&v=2AAa0gd7ClM" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/twitter_1x_v2.png" alt=""> / ucbcomedy</a></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>