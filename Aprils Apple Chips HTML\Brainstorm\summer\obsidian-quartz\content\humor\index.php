<?php
// Auto-generated category index
// Category: humor

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Humor Vault - A. A. Chips';
$meta_description = 'Here\'s a bunch of funny things I\'ve saved and bookmarked off the internet.';
$meta_keywords = 'humor, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Alanis Morissette Narrates Your Day',
    'author' => 'Holderness Family',
    'date' => '2023-01-11',
    'excerpt' => '--- [Alanis Morissette Narrates Your Day - YouTube](https://www.youtube.com/watch?v=0smCdKsvmg0) # Alanis Morissette Narrates Your Day [Holderness Fam...',
    'url' => 'alanis-narrates-your-day.php',
    'tags' => 
    array (
      0 => 'memes',
      1 => 'humor',
    ),
    'filename' => 'alanis-narrates-your-day',
    'thumbnail' => NULL,
  ),
  1 => 
  array (
    'title' => 'BP Spills Coffee: A Parody by UCB Comedy
"Date:": June 9, 2010',
    'author' => NULL,
    'date' => NULL,
    'excerpt' => '\'This is what happens when BP spills coffee.\'',
    'url' => 'bp-coffee.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'bp-coffee',
    'thumbnail' => NULL,
  ),
  2 => 
  array (
    'title' => 'FBI Releases List of 10 Weirdest People Who Are Actually Harmless Once You Get to Know Them',
    'author' => 'The Onion',
    'date' => NULL,
    'excerpt' => '# FBI Releases List Of 10 Weirdest People Who Are Actually Harmless Once You Get To Know Them ![[weirdestpeople.webp]] WASHINGTON—Emphasizing that i...',
    'url' => 'theonion-ten-weirdest-people.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'theonion-ten-weirdest-people',
    'thumbnail' => NULL,
  ),
  3 => 
  array (
    'title' => 'Friends.. but it\'s all wrong - There I Ruined It',
    'author' => 'There I Ruined It
"Date:": March 31, 2025',
    'date' => NULL,
    'excerpt' => 'Friends.. but it\'s all wrong',
    'url' => 'friends-all-wrong.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'friends-all-wrong',
    'thumbnail' => NULL,
  ),
  4 => 
  array (
    'title' => 'L.P.D.: Libertarian Police Department',
    'author' => 'Tom O\'Donnell',
    'date' => NULL,
    'excerpt' => '“Home Depot™ Presents the Police!®” I said, flashing my badge and my gun and a small picture of Ron Paul. “Nobody move unless you want to!” They didn’t.',
    'url' => 'libertarian-pd.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'libertarian-pd',
    'thumbnail' => NULL,
  ),
  5 => 
  array (
    'title' => 'Learn Attack: Could Humans One Day Die on Mars',
    'author' => NULL,
    'date' => '2017-03-06',
    'excerpt' => 'Prepare to have your knowledge receptors jammed with facts on ‘Learn Attack,’ a brand-new science series from ClickHole. In this episode, David asks: Could humans colonize Mars? The answer might surprise you.',
    'url' => 'learn-attack.php',
    'tags' => 
    array (
      0 => 'humor',
    ),
    'filename' => 'learn-attack',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'List of things to say when someone asks why you don\'t want kids',
    'author' => 'nerdfighterwhatevernumbers on Pinterest',
    'date' => NULL,
    'excerpt' => 'Reposted from nerdfighterwhatevernumbers on Pinterest.com ## List of things to say when someone asks why you don\'t want kids + I promised my firstborn...',
    'url' => 'dont-want-kids.php',
    'tags' => 
    array (
      0 => 'humor',
    ),
    'filename' => 'dont-want-kids',
    'thumbnail' => NULL,
  ),
  7 => 
  array (
    'title' => 'Manhunt for Ed Sheeran',
    'author' => 'Unknown',
    'date' => NULL,
    'excerpt' => 'The fugitive task force is looking for Ronnie Williams Jr. of Cincinnati for fraud. He has convinced 3 local churches that he is Ed Sheeran and even went as far as performing The Shape Of You in front of Christ Community Church last Sunday.',
    'url' => 'manhunt-ed-sheeran.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'manhunt-ed-sheeran',
    'thumbnail' => NULL,
  ),
  8 => 
  array (
    'title' => 'The Diarrhea Brothers Save the Day',
    'author' => 'Joel Haver',
    'date' => NULL,
    'excerpt' => 'John, Jonathan and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by Joel Haver',
    'url' => 'db-bros-save-day.php',
    'tags' => 
    array (
      0 => 'humor',
    ),
    'filename' => 'db-bros-save-day',
    'thumbnail' => NULL,
  ),
  9 => 
  array (
    'title' => 'The Place where Nothing Bad Happens',
    'author' => 'Stanzi',
    'date' => NULL,
    'excerpt' => ' # The place where nothing bad happens. [#shorts](https://www.youtube.com/hashtag/shorts) [Stanzi](https://www.youtube.com/@Stanzipotenza) 2.64M subsc...',
    'url' => 'where-nothing-bad-happens.php',
    'tags' => 
    array (
      0 => 'humor',
    ),
    'filename' => 'where-nothing-bad-happens',
    'thumbnail' => NULL,
  ),
  10 => 
  array (
    'title' => 'Toilet Paper Bears',
    'author' => 'Joel Haver',
    'date' => '2022-07-14',
    'excerpt' => 'Animated skit by Joel Haver. Possibly stolen by Saturday Night Live. You can read more about the scandal and controversy.',
    'url' => 'tp-bears.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'tp-bears',
    'thumbnail' => NULL,
  ),
  11 => 
  array (
    'title' => 'Visit the Titanic Today',
    'author' => 'Stanzi',
    'date' => NULL,
    'excerpt' => ' [Visit the Titanic today #shorts - YouTube](https://www.youtube.com/shorts/ImlQKj20EQM)',
    'url' => 'visit-titanic.php',
    'tags' => 
    array (
      0 => '#humor',
    ),
    'filename' => 'visit-titanic',
    'thumbnail' => NULL,
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<p>Here is some funny things I've collected from the internet. I am sharing these in good faith that the creators are okay with their content being shared and celebrated. Original channels are linked in each page if it exists.</p>


<ul>
<ul><li>
<p><a href="alanis-narrates-your-day.php" class="internal-link">Alanis Morissette Narrates Your Day</a></p>
<p></li></p>
<p><li></p>
<p><a href="bp-coffee.php" class="internal-link">BP Spills Coffee: A Parody by UCB Comedy</a></p>
<p></li></p>
<p><li></p>
<p><a href="friends-all-wrong.php" class="internal-link">Friends is All Wrong</a></p>
<p></li></p>
<p><li></p>
<p><a href="you-died-youtube-short.php" class="internal-link">You Died - Youtube Short</a></p>
<p></li></p>
<p><li></p>
<p><a href="learn-attack.php" class="internal-link">Learn Attack: Could Humans One Day Die on Mars?</a></p>
<p></li></p>
<p><li></p>
<p><a href="libertarian-pd.php" class="internal-link">L.P.D.: Libertarian Police Department</a></p>
<p></li></p>
<p><li></p>
<p><a href="manhunt-ed-sheeran.php" class="internal-link">Manhunt (Ed Sheeran Parody)</a></p>
<p></li></p>
<p><li></p>
<p><a href="theonion-ten-weirdest-people.php" class="internal-link">The Onion: Ten Weirdest People You Know</a></p>
<p></li></p>
<p><li></p>
<p><a href="cant-believe-hes-gone.php" class="internal-link">Can't Believe He's Gone</a></p>
<p></li></p>
<p><li></p>
<p><a href="db-bros-save-day.php" class="internal-link">The Diarrhea Brothers Save the Day</a></p>
<p></li></p>
<p><li></p>
<p><a href="where-nothing-bad-happens.php" class="internal-link">The Place where Nothing Bad Happens</a></p>
<p></li></p>
<p><li></p>
<p><a href="meme-collection.php" class="internal-link">Meme Collection</a></p>
<p></li></p>
<p><li></p>
<p><a href="tp-bears.php" class="internal-link">Toilet Paper Bears</a></p>
<p></li></p>
<p><li></p>
<p><a href="visit-titanic.php" class="internal-link">Visit Titanic</a></p>
<p></li></ul></p>
<p></ul></p>

HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author']): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>