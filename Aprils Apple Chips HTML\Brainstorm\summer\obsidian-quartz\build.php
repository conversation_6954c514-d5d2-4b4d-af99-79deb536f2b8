<?php
/**
 * Enhanced Blog Builder
 * Converts Markdown files with YAML frontmatter to dynamic PHP pages
 * Now with centralized path management to resolve all path-related issues
 */

require_once 'path-helper.php';

class BlogBuilder {
    private $contentDir = 'content';
    private $outputDir = 'content'; // Generate files in same directory as markdown
    private $dataDir = 'data';
    private $templateFile = 'page template.htm';
    private $configFile = 'config.php';
    private $siteConfig = [];

    public function __construct() {
        // Load site configuration
        if (file_exists($this->configFile)) {
            $this->siteConfig = include $this->configFile;
        } else {
            throw new Exception("Configuration file not found: {$this->configFile}");
        }

        // Create output directories if they don't exist
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    public function build() {
        echo "Starting blog build...\n";

        // Create placeholder images directory if it doesn't exist
        $placeholderDir = 'img/thumbs';
        if (!is_dir($placeholderDir)) {
            mkdir($placeholderDir, 0755, true);
        }

        // Check if placeholder images exist, if not create them
        $this->ensurePlaceholderImages($placeholderDir);

        // Process all markdown files
        $this->processMarkdownFiles();

        // Generate JSON data for shorthand content
        $this->generateHumorData();

        // Generate category index pages
        $this->generateCategoryIndexes();

        // Generate main index page
        $this->generateIndexPage();

        echo "Build complete!\n";
    }

    private function ensurePlaceholderImages($dir) {
        $placeholders = [
            'placeholder1.jpg',
            'placeholder2.jpg',
            'placeholder3.jpg',
            'placeholder4.jpg'
        ];
        
        foreach ($placeholders as $placeholder) {
            $path = $dir . '/' . $placeholder;
            if (!file_exists($path)) {
                echo "Note: Placeholder image $placeholder not found. Please add it to $dir directory.\n";
            }
        }
    }

    private function processMarkdownFiles() {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->contentDir)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $this->processMarkdownFile($file);
            }
        }
    }

    private function processMarkdownFile($file) {
        $content = file_get_contents($file->getPathname());
        $parsed = $this->parseMarkdown($content);

        // Skip if no frontmatter
        if (!$parsed['frontmatter']) {
            return;
        }

        $frontmatter = $parsed['frontmatter'];
        $markdownContent = $parsed['content'];

        // Add source file information to frontmatter
        $frontmatter['source_file'] = $file->getPathname();

        // Get relative path for image handling
        $relativePath = str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
        $relativePath = dirname($relativePath);
        if ($relativePath === '.') {
            $relativePath = '';
        }

        // Convert markdown to HTML
        $htmlContent = $this->markdownToHtml($markdownContent, $relativePath);

        // Determine if this should be shorthand content (humor section)
        $isShorthand = $this->isShorthandContent($file, $frontmatter);

        if ($isShorthand) {
            // Store for JSON generation instead of creating HTML file
            return;
        }

        // Generate HTML file
        $this->generateHtmlFile($file, $frontmatter, $htmlContent);
    }

    private function parseMarkdown($content) {
        // Simple YAML frontmatter parser
        if (!preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            return ['frontmatter' => null, 'content' => $content];
        }

        $frontmatter = [];
        $yamlLines = explode("\n", $matches[1]);
        $currentKey = null;
        $inArray = false;
        $arrayValues = [];

        foreach ($yamlLines as $line) {
            // Skip empty lines
            if (trim($line) === '') {
                continue;
            }

            // Check if this is a new key
            if (preg_match('/^([a-zA-Z0-9_-]+):\s*(.*)$/', $line, $keyMatch)) {
                // Save previous array if we were building one
                if ($inArray && $currentKey) {
                    $frontmatter[$currentKey] = $arrayValues;
                    $arrayValues = [];
                    $inArray = false;
                }

                $currentKey = strtolower($keyMatch[1]); // Normalize key to lowercase
                $value = trim($keyMatch[2]);

                // Check if this is the start of an array
                if ($value === '') {
                    $inArray = true;
                } else {
                    // Handle quoted strings
                    if (preg_match('/^"(.*)"$/', $value, $stringMatch)) {
                        $value = $stringMatch[1];
                    } elseif (preg_match('/^\'(.*)\'$/', $value, $stringMatch)) {
                        $value = $stringMatch[1];
                    }

                    // Special handling for date field - validate format
                    if ($currentKey === 'date') {
                        $value = $this->validateAndFormatDate($value);
                    }

                    $frontmatter[$currentKey] = $value;
                }
            }
            // Check if this is an array item
            elseif (preg_match('/^\s*-\s*(.*)$/', $line, $arrayMatch) && $inArray) {
                $value = trim($arrayMatch[1]);
                // Handle quoted strings in arrays
                if (preg_match('/^"(.*)"$/', $value, $stringMatch)) {
                    $value = $stringMatch[1];
                } elseif (preg_match('/^\'(.*)\'$/', $value, $stringMatch)) {
                    $value = $stringMatch[1];
                }
                $arrayValues[] = $value;
            }
            // Handle continuation of previous value (multiline)
            elseif ($currentKey && !$inArray) {
                $frontmatter[$currentKey] .= "\n" . trim($line);
            }
        }

        // Save final array if we were building one
        if ($inArray && $currentKey) {
            $frontmatter[$currentKey] = $arrayValues;
        }

        return ['frontmatter' => $frontmatter, 'content' => $matches[2]];
    }

    private function validateAndFormatDate($dateValue) {
        // Check if it's already in YYYY-MM-DD format
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateValue)) {
            // Validate that it's a real date
            $dateParts = explode('-', $dateValue);
            if (checkdate($dateParts[1], $dateParts[2], $dateParts[0])) {
                return $dateValue;
            }
        }

        // Try to parse other date formats and convert to YYYY-MM-DD
        $timestamp = strtotime($dateValue);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }

        // If all else fails, return the original value
        echo "Warning: Invalid date format '$dateValue' - using as-is\n";
        return $dateValue;
    }

    private function markdownToHtml($markdown, $relativePath = '') {
        // Simple markdown to HTML conversion
        $html = $markdown;

        // Headers (but don't convert if it's already HTML)
        $html = preg_replace('/^### (.*$)/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.*$)/m', '<h2>$1</h2>', $html);
        // Don't convert # headers if they're already in HTML format
        $html = preg_replace('/^# (?!.*<h1>)(.*$)/m', '<h1>$1</h1>', $html);

        // Bold and italic
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);

        // Images - handle relative paths correctly (PROCESS FIRST before internal links)
        $imgBasePath = $this->calculateImagePath($relativePath);

        // Process Obsidian-style image links ![[image.ext]]
        $html = preg_replace_callback('/!\[\[([^\]]+)\]\]/', function($matches) use ($imgBasePath) {
            $imageName = preg_replace('/\|.*$/', '', $matches[1]); // Remove size specifications
            $imageName = trim($imageName);
            return '<img src="' . $imgBasePath . $imageName . '" alt="' . htmlspecialchars($imageName) . '">';
        }, $html);

        // Process standard markdown image links ![alt](src)
        $html = preg_replace('/!\[([^\]]*)\]\(([^)]+)\)/', '<img src="$2" alt="$1">', $html);

        // Process internal wiki-style links AFTER images
        $html = $this->processInternalLinks($html, $relativePath);

        // Regular markdown links
        $html = preg_replace_callback('/\[([^\]]+)\]\(([^)]+)\)/', function($matches) {
            $text = $matches[1];
            $url = $matches[2];
            $class = $this->isExternalLink($url) ? 'external-link' : 'internal-link';
            return '<a href="' . $url . '" class="' . $class . '">' . $text . '</a>';
        }, $html);

        // Lists
        $html = preg_replace('/^\+ (.*)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/^- (.*)$/m', '<li>$1</li>', $html);

        // Wrap consecutive list items in ul tags
        $html = preg_replace('/(<li>.*<\/li>)/s', '<ul>$1</ul>', $html);

        // Paragraphs
        $lines = explode("\n", $html);
        $result = [];

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                $result[] = '';
                continue;
            }

            // Don't wrap HTML tags in paragraphs
            if (strpos($line, '<h') === 0 || strpos($line, '<ul>') === 0 ||
                strpos($line, '<img') === 0 || strpos($line, '<iframe') === 0 ||
                strpos($line, '<div') === 0 || strpos($line, '<hr') === 0 ||
                strpos($line, '</div>') === 0) {
                $result[] = $line;
            } else {
                $result[] = "<p>{$line}</p>";
            }
        }

        return implode("\n", $result);
    }

    private function calculateImagePath($relativePath) {
        // Use PathHelper to calculate the correct image path
        $depth = 0;
        if (!empty($relativePath)) {
            $depth = PathHelper::calculateDepthFromPath($relativePath);
        }

        // Set the depth temporarily to calculate the correct path
        $originalDepth = PathHelper::getCurrentDepth();
        PathHelper::setCurrentDepth($depth);
        $imagePath = PathHelper::getImagesPath();
        PathHelper::setCurrentDepth($originalDepth);

        return $imagePath;
    }

    private function processInternalLinks($html, $relativePath) {
        // Process wiki-style links [[page-name]] and [[page-name|display text]]
        return preg_replace_callback('/\[\[([^\]]+)\]\]/', function($matches) use ($relativePath) {
            $linkContent = $matches[1];

            // Check if there's a pipe separator for custom display text
            if (strpos($linkContent, '|') !== false) {
                list($pageName, $displayText) = explode('|', $linkContent, 2);
                $pageName = trim($pageName);
                $displayText = trim($displayText);
            } else {
                $pageName = trim($linkContent);
                $displayText = $pageName;
            }

            // Convert page name to URL
            $url = $this->pageNameToUrl($pageName, $relativePath);

            return '<a href="' . $url . '" class="internal-link">' . htmlspecialchars($displayText) . '</a>';
        }, $html);
    }

    private function pageNameToUrl($pageName, $relativePath) {
        // Use PathHelper to resolve internal links
        $depth = 0;
        if (!empty($relativePath)) {
            $depth = PathHelper::calculateDepthFromPath($relativePath);
        }

        // Set the depth temporarily to calculate the correct path
        $originalDepth = PathHelper::getCurrentDepth();
        PathHelper::setCurrentDepth($depth);
        $url = PathHelper::resolveInternalLink($pageName, $relativePath);
        PathHelper::setCurrentDepth($originalDepth);

        return $url;
    }

    private function isExternalLink($url) {
        // Check if URL is external (starts with http/https or contains domain)
        return preg_match('/^https?:\/\//', $url) || strpos($url, '://') !== false;
    }

    private function isShorthandContent($file, $frontmatter) {
        // For now, we don't treat any content as shorthand - all posts get individual pages
        // The humor JSON data is generated separately for modal display
        return false;
    }

    private function generateHtmlFile($file, $frontmatter, $content) {
        // Generate output filename in the same directory as the markdown file
        $extension = $this->siteConfig['build']['generate_php'] ? '.php' : '.html';
        $outputPath = str_replace('.md', $extension, $file->getPathname());

        // Generate PHP or HTML file
        if ($this->siteConfig['build']['generate_php']) {
            // Generate PHP file with dynamic content
            $phpContent = $this->generatePhpFile($frontmatter, $content);
            file_put_contents($outputPath, $phpContent);
        } else {
            // Generate static HTML file
            $this->generateStaticHtmlFile($file, $frontmatter, $content, $outputPath);
        }

        echo "Generated: $outputPath\n";
    }

    private function generateStaticHtmlFile($file, $frontmatter, $content, $outputPath) {
        // Load template
        $template = file_get_contents($this->templateFile);

        // Prepare variables for template
        $page_title = $frontmatter['title'] ?? 'Untitled';
        $css_path = '../css/';
        $js_path = '../js/';
        $base_url = '../';

        // For static HTML generation, create the full content with post header
        $postHeader = $this->generatePostHeader($frontmatter);
        $postFooter = "\n</div><!-- .post-content -->\n";
        $fullContent = $postHeader . $content . $postFooter;

        // Prepare meta data
        $meta_description = $frontmatter['excerpt'] ?? $frontmatter['description'] ?? '';
        $meta_keywords = '';
        if (isset($frontmatter['tags'])) {
            $tags = is_array($frontmatter['tags']) ? $frontmatter['tags'] : [$frontmatter['tags']];
            $meta_keywords = implode(', ', $tags) . ', ' . ($this->siteConfig['meta']['keywords'] ?? '');
        }

        // Start output buffering to capture PHP includes
        ob_start();

        // Set variables for template
        extract([
            'config' => $this->siteConfig,
            'page_title' => $page_title,
            'content' => $fullContent,
            'css_path' => $css_path,
            'js_path' => $js_path,
            'base_url' => $base_url,
            'meta_description' => $meta_description,
            'meta_keywords' => $meta_keywords,
            'related_posts' => []
        ]);

        // Evaluate the template
        eval('?>' . $template);
        $html = ob_get_clean();

        // Write static HTML file
        file_put_contents($outputPath, $html);
    }

    private function generatePhpFile($frontmatter, $content) {
        // Prepare variables for PHP file
        $page_title = $frontmatter['title'] ?? 'Untitled';
        $meta_description = $frontmatter['excerpt'] ?? $frontmatter['description'] ?? '';
        $meta_keywords = '';
        if (isset($frontmatter['tags'])) {
            $tags = is_array($frontmatter['tags']) ? $frontmatter['tags'] : [$frontmatter['tags']];
            $meta_keywords = implode(', ', $tags) . ', ' . ($this->siteConfig['meta']['keywords'] ?? '');
        }

        // Create PHP file content with dynamic post generation
        $phpContent = "<?php\n";
        $phpContent .= "// Auto-generated blog post\n";
        $phpContent .= "// Source: " . basename($frontmatter['source_file'] ?? 'unknown') . "\n\n";

        // Calculate relative paths based on file location using PathHelper
        $sourceFile = $frontmatter['source_file'] ?? '';
        $relativePath = str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', dirname($sourceFile));
        $depth = PathHelper::calculateDepthFromPath($relativePath);

        $pathPrefix = str_repeat('../', $depth);
        $phpContent .= "// Load path helper and configuration with fallback\n";
        $phpContent .= "\$pathPrefix = " . var_export($pathPrefix, true) . ";\n";
        $phpContent .= "if (file_exists(__DIR__ . '/' . \$pathPrefix . 'path-helper.php')) {\n";
        $phpContent .= "    require_once __DIR__ . '/' . \$pathPrefix . 'path-helper.php';\n";
        $phpContent .= "    \$config = include __DIR__ . '/' . \$pathPrefix . 'config.php';\n";
        $phpContent .= "} elseif (file_exists(__DIR__ . '/' . \$pathPrefix . '../path-helper.php')) {\n";
        $phpContent .= "    require_once __DIR__ . '/' . \$pathPrefix . '../path-helper.php';\n";
        $phpContent .= "    \$config = include __DIR__ . '/' . \$pathPrefix . '../config.php';\n";
        $phpContent .= "} else {\n";
        $phpContent .= "    die('Could not find path-helper.php');\n";
        $phpContent .= "}\n";
        $phpContent .= "\$paths = initPaths(\$config, __FILE__);\n\n";

        $phpContent .= "// Page variables\n";
        $phpContent .= "\$page_title = " . var_export($page_title, true) . ";\n";
        $phpContent .= "\$meta_description = " . var_export($meta_description, true) . ";\n";
        $phpContent .= "\$meta_keywords = " . var_export($meta_keywords, true) . ";\n";
        $phpContent .= "\$css_path = \$paths['css_path'];\n";
        $phpContent .= "\$js_path = \$paths['js_path'];\n";
        $phpContent .= "\$base_url = \$paths['base_path'];\n";
        
        // Add thumbnail path if available
        if (isset($frontmatter['thumbnail'])) {
            $phpContent .= "\$thumbnail = \$paths['base_path'] . 'img/thumbs/' . " . var_export($frontmatter['thumbnail'], true) . ";\n";
        } else {
            $phpContent .= "\$thumbnail = null;\n";
        }

        // Generate related posts (category links) using path constants
        $phpContent .= "\$related_posts = [\n";
        $categories = $this->getContentCategories();
        foreach ($categories as $category) {
            $categoryTitle = ucwords(str_replace('-', ' ', $category));
            $phpContent .= "    ['title' => '{$categoryTitle}', 'url' => PathHelper::getCategoryIndexPath('{$category}'), 'excerpt' => 'Browse all {$categoryTitle} posts'],\n";
        }
        $phpContent .= "];\n\n";

        // Generate random posts for sidebar
        $phpContent .= "\$random_posts = [];\n";
        $phpContent .= "// This will be populated by the sidebar include\n\n";

        $phpContent .= "// Post metadata\n";
        $phpContent .= "\$post_data = " . var_export($frontmatter, true) . ";\n\n";

        $phpContent .= "// Raw content\n";
        $phpContent .= "\$post_content = " . var_export($content, true) . ";\n\n";

        $phpContent .= "// Generate dynamic content\n";
        $phpContent .= "ob_start();\n";
        $phpContent .= "?>\n";
        $phpContent .= "<article class=\"post-header\">\n";
        $phpContent .= "    <?php if (isset(\$post_data['title'])): ?>\n";
        $phpContent .= "        <h1 class=\"post-title\"><?php echo htmlspecialchars(\$post_data['title']); ?></h1>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php \$metadata = []; ?>\n";
        $phpContent .= "    <?php if (isset(\$post_data['author'])): ?>\n";
        $phpContent .= "        <?php \$metadata[] = '<span class=\"post-author\"><i class=\"icon-user\"></i>By ' . htmlspecialchars(\$post_data['author']) . '</span>'; ?>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    <?php if (isset(\$post_data['date'])): ?>\n";
        $phpContent .= "        <?php \$formatted_date = (strtotime(\$post_data['date']) !== false) ? date('F j, Y', strtotime(\$post_data['date'])) : htmlspecialchars(\$post_data['date']); ?>\n";
        $phpContent .= "        <?php \$metadata[] = '<span class=\"post-date\"><i class=\"icon-calendar\"></i>' . \$formatted_date . '</span>'; ?>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php if (!empty(\$metadata)): ?>\n";
        $phpContent .= "        <div class=\"post-meta\"><?php echo implode('<span class=\"meta-separator\"> • </span>', \$metadata); ?></div>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php if (isset(\$post_data['excerpt'])): ?>\n";
        $phpContent .= "        <div class=\"post-excerpt\">\n";
        $phpContent .= "            <p><em><?php echo htmlspecialchars(\$post_data['excerpt']); ?></em></p>\n";
        $phpContent .= "        </div>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php if (isset(\$post_data['tags'])): ?>\n";
        $phpContent .= "        <?php \$tags = is_array(\$post_data['tags']) ? \$post_data['tags'] : [\$post_data['tags']]; ?>\n";
        $phpContent .= "        <?php if (!empty(\$tags)): ?>\n";
        $phpContent .= "            <div class=\"post-tags\">\n";
        $phpContent .= "                <span class=\"tags-label\">Tags:</span>\n";
        $phpContent .= "                <?php foreach (\$tags as \$tag): ?>\n";
        $phpContent .= "                    <?php \$tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim(\$tag)))); ?>\n";
        $phpContent .= "                    <a href=\"tag-<?php echo \$tag_slug; ?>.html\" class=\"tag\"><?php echo htmlspecialchars(\$tag); ?></a>\n";
        $phpContent .= "                <?php endforeach; ?>\n";
        $phpContent .= "            </div>\n";
        $phpContent .= "        <?php endif; ?>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "</article>\n\n";
        $phpContent .= "<div class=\"post-content\">\n";
        $phpContent .= "    <?php echo \$post_content; ?>\n";
        $phpContent .= "</div><!-- .post-content -->\n";
        $phpContent .= "<?php\n";
        $phpContent .= "\$content = ob_get_clean();\n\n";

        $phpContent .= "// Include template\n";
        $phpContent .= "include \$paths['template_path'];\n";
        $phpContent .= "?>";

        return $phpContent;
    }

    private function generatePostHeader($frontmatter) {
        if (empty($frontmatter)) {
            return '';
        }

        $header = "<article class=\"post-header\">\n";

        // Add thumbnail if available
        if (isset($frontmatter['thumbnail'])) {
            $header .= "    <div class=\"post-thumbnail\">\n";
            $header .= "        <img src=\"../img/thumbs/" . htmlspecialchars($frontmatter['thumbnail']) . "\" alt=\"" . 
                       htmlspecialchars($frontmatter['title'] ?? 'Post thumbnail') . "\" class=\"featured-image\">\n";
            $header .= "    </div>\n";
        }

        // Add title
        if (isset($frontmatter['title'])) {
            $header .= "    <h1 class=\"post-title\">" . htmlspecialchars($frontmatter['title']) . "</h1>\n";
        }

        // Add post metadata
        $metadata = [];
        if (isset($frontmatter['author'])) {
            $metadata[] = "<span class=\"post-author\"><i class=\"icon-user\"></i>By " . htmlspecialchars($frontmatter['author']) . "</span>";
        }
        if (isset($frontmatter['date'])) {
            $formattedDate = $this->formatDate($frontmatter['date']);
            $metadata[] = "<span class=\"post-date\"><i class=\"icon-calendar\"></i>{$formattedDate}</span>";
        }
        if (isset($frontmatter['reading_time'])) {
            $metadata[] = "<span class=\"post-reading-time\"><i class=\"icon-clock\"></i>{$frontmatter['reading_time']} min read</span>";
        }

        if (!empty($metadata)) {
            $header .= "    <div class=\"post-meta\">" . implode('<span class=\"meta-separator\"> • </span>', $metadata) . "</div>\n";
        }

        // Add excerpt if available
        if (isset($frontmatter['excerpt'])) {
            $header .= "    <div class=\"post-excerpt\">\n";
            $header .= "        <p><em>" . htmlspecialchars($frontmatter['excerpt']) . "</em></p>\n";
            $header .= "    </div>\n";
        }

        // Add tags if available
        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            if (is_array($tags) && !empty($tags)) {
                $header .= "    <div class=\"post-tags\">\n";
                $header .= "        <span class=\"tags-label\">Tags:</span>\n";
                foreach ($tags as $tag) {
                    $tagSlug = $this->slugify($tag);
                    $header .= "        <a href=\"tag-{$tagSlug}.html\" class=\"tag\">" . htmlspecialchars($tag) . "</a>\n";
                }
                $header .= "    </div>\n";
            } elseif (is_string($tags)) {
                $tagSlug = $this->slugify($tags);
                $header .= "    <div class=\"post-tags\">\n";
                $header .= "        <span class=\"tags-label\">Tags:</span>\n";
                $header .= "        <a href=\"tag-{$tagSlug}.html\" class=\"tag\">" . htmlspecialchars($tags) . "</a>\n";
                $header .= "    </div>\n";
            }
        }

        $header .= "</article>\n\n";
        $header .= "<div class=\"post-content\">\n\n";

        return $header;
    }

    private function formatDate($date) {
        // Try to parse and format the date
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            return date('F j, Y', $timestamp);
        }
        return htmlspecialchars($date); // Return as-is if parsing fails
    }

    private function slugify($text) {
        // Convert to lowercase and replace spaces/special chars with hyphens
        $slug = strtolower(trim($text));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }

    private function generateHumorData() {
        $humorData = [];
        $humorDir = $this->contentDir . '/humor';

        if (!is_dir($humorDir)) {
            file_put_contents($this->dataDir . '/humor.json', json_encode([]));
            return;
        }

        $files = glob($humorDir . '/*.md');

        foreach ($files as $file) {
            $content = file_get_contents($file);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $htmlContent = $this->markdownToHtml($parsed['content'], 'humor');

                $humorData[] = [
                    'title' => $frontmatter['title'] ?? basename($file, '.md'),
                    'author' => $frontmatter['author'] ?? null,
                    'date' => $frontmatter['date'] ?? null,
                    'excerpt' => $this->generateExcerpt($parsed['content']),
                    'content' => $htmlContent,
                    'tags' => $frontmatter['tags'] ?? []
                ];
            }
        }

        file_put_contents($this->dataDir . '/humor.json', json_encode($humorData, JSON_PRETTY_PRINT));
        echo "Generated humor data: " . count($humorData) . " items\n";
    }

    private function generateIndexPage() {
        // Check if index.md exists in content directory
        $indexFile = $this->contentDir . '/index.md';

        if (file_exists($indexFile)) {
            // Process the existing index.md file
            $content = file_get_contents($indexFile);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $htmlContent = $this->markdownToHtml($parsed['content'], '');

                // Generate index.html in root directory
                $this->generateRootIndexFile($frontmatter, $htmlContent);
            }
        } else {
            // Generate a default index page
            $this->generateDefaultIndexPage();
        }
    }

    private function generateRootIndexFile($frontmatter, $content) {
        // Load template
        $template = file_get_contents($this->templateFile);

        // Prepare variables for template - ensure clean title without frontmatter pollution
        $page_title = isset($frontmatter['title']) ? $frontmatter['title'] : ($this->siteConfig['site']['title'] ?? 'Welcome to A. A. Chips\' Blog');
        $css_path = 'css/';
        $js_path = 'js/';
        $base_url = '';

        // Prepare meta data
        $meta_description = isset($frontmatter['description']) ? $frontmatter['description'] : ($this->siteConfig['site']['description'] ?? '');
        $meta_keywords = $this->siteConfig['meta']['keywords'] ?? '';

        // Use a clean function to evaluate the template without variable pollution
        $html = $this->evaluateTemplate($template, [
            'config' => $this->siteConfig,
            'page_title' => $page_title,
            'content' => $content,
            'css_path' => $css_path,
            'js_path' => $js_path,
            'base_url' => $base_url,
            'meta_description' => $meta_description,
            'meta_keywords' => $meta_keywords,
            'related_posts' => []
        ]);

        // Write index.html file in root
        file_put_contents('index.html', $html);
        echo "Generated: index.html\n";
    }

    private function evaluateTemplate($template, $variables) {
        // Start output buffering
        ob_start();

        // Extract only the provided variables
        extract($variables);

        // Evaluate the template in a clean scope
        eval('?>' . $template);

        // Return the captured output
        return ob_get_clean();
    }

    private function generateDefaultIndexPage() {
        $defaultContent = '<h1>Welcome to A. A. Chips\' Digital Garden</h1>
        <p>This is a simple blog system built from Markdown files.</p>
        <p>Check out the generated content in the <a href="generated/">generated folder</a>.</p>';

        $this->generateRootIndexFile(['title' => 'A. A. Chips\' Blog'], $defaultContent);
    }

    private function generateExcerpt($content, $length = 150) {
        $text = strip_tags($content);
        $text = preg_replace('/\s+/', ' ', $text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }

    private function generateCategoryIndexes() {
        echo "Generating category index pages...\n";

        // Get all subdirectories in content
        $categories = $this->getContentCategories();

        foreach ($categories as $category) {
            $this->generateCategoryIndex($category);
        }
    }

    private function getContentCategories() {
        $categories = [];
        $iterator = new DirectoryIterator($this->contentDir);

        foreach ($iterator as $item) {
            if ($item->isDot() || !$item->isDir()) {
                continue;
            }

            $categoryName = $item->getFilename();
            // Skip special directories
            if (in_array($categoryName, ['playlists', 'gallery'])) {
                continue;
            }

            $categories[] = $categoryName;
        }

        return $categories;
    }

    private function generateCategoryIndex($category) {
        $categoryDir = $this->contentDir . DIRECTORY_SEPARATOR . $category;
        $indexPath = $categoryDir . DIRECTORY_SEPARATOR . 'index.php';
        $indexMdPath = $categoryDir . DIRECTORY_SEPARATOR . 'index.md';

        // Get all posts in this category
        $posts = $this->getCategoryPosts($category);

        if (empty($posts)) {
            echo "No posts found for category: $category\n";
            return;
        }

        // Check if index.md exists and use its content
        $customContent = '';
        $frontmatter = [];
        if (file_exists($indexMdPath)) {
            $mdContent = file_get_contents($indexMdPath);
            $parsed = $this->parseMarkdown($mdContent);
            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
            }
            // Convert markdown content to HTML
            $customContent = $this->markdownToHtml($parsed['content'], $category);
        }

        // Generate category index content
        $content = $this->generateCategoryIndexContent($category, $posts, $customContent, $frontmatter);

        // Write the index file
        file_put_contents($indexPath, $content);
        echo "Generated category index: $indexPath\n";
    }

    private function generateCategoryIndexContent($category, $posts, $customContent = '', $frontmatter = []) {
        $categoryTitle = isset($frontmatter['title']) ? $frontmatter['title'] : ucwords(str_replace('-', ' ', $category));

        $phpContent = "<?php\n";
        $phpContent .= "// Auto-generated category index\n";
        $phpContent .= "// Category: $category\n\n";

        // Use the same path helper approach as in the individual post files
        $phpContent .= "// Load path helper and configuration with fallback\n";
        $phpContent .= "\$pathPrefix = '../';\n";
        $phpContent .= "if (file_exists(__DIR__ . '/' . \$pathPrefix . 'path-helper.php')) {\n";
        $phpContent .= "    require_once __DIR__ . '/' . \$pathPrefix . 'path-helper.php';\n";
        $phpContent .= "    \$config = include __DIR__ . '/' . \$pathPrefix . 'config.php';\n";
        $phpContent .= "} elseif (file_exists(__DIR__ . '/' . \$pathPrefix . '../path-helper.php')) {\n";
        $phpContent .= "    require_once __DIR__ . '/' . \$pathPrefix . '../path-helper.php';\n";
        $phpContent .= "    \$config = include __DIR__ . '/' . \$pathPrefix . '../config.php';\n";
        $phpContent .= "} else {\n";
        $phpContent .= "    die('Could not find path-helper.php');\n";
        $phpContent .= "}\n";
        $phpContent .= "\$paths = initPaths(\$config, __FILE__);\n\n";

        $phpContent .= "// Page variables\n";
        $phpContent .= "\$page_title = " . var_export($categoryTitle, true) . ";\n";
        $phpContent .= "\$meta_description = " . var_export(isset($frontmatter['excerpt']) ? $frontmatter['excerpt'] : "Browse all posts in the $categoryTitle category", true) . ";\n";
        $phpContent .= "\$meta_keywords = '$category, posts, A. A. Chips, blog';\n";
        $phpContent .= "\$css_path = \$paths['css_path'];\n";
        $phpContent .= "\$js_path = \$paths['js_path'];\n";
        $phpContent .= "\$base_url = \$paths['base_path'];\n";
        $phpContent .= "\$related_posts = [];\n\n";

        $phpContent .= "// Category posts data\n";
        $phpContent .= "\$category_posts = " . var_export($posts, true) . ";\n\n";

        $phpContent .= "// Generate content\n";
        $phpContent .= "ob_start();\n";
        $phpContent .= "?>\n";
        $phpContent .= "<div class=\"category-index\">\n";
        $phpContent .= "    <header class=\"category-header\">\n";
        $phpContent .= "        <h1><?php echo htmlspecialchars(\$page_title); ?></h1>\n";
        
        // Only add the default description if no custom content
        if (empty($customContent)) {
            $phpContent .= "        <p class=\"category-description\">All posts in the <?php echo htmlspecialchars(\$page_title); ?> category</p>\n";
        }
        $phpContent .= "    </header>\n\n";
        
        // Include custom content from index.md if available
        if (!empty($customContent)) {
            $phpContent .= "    <div class=\"category-content\">\n";
            $phpContent .= "        <?php echo <<<'HTML'\n";
            $phpContent .= $customContent . "\n";
            $phpContent .= "HTML;\n";
            $phpContent .= "        ?>\n";
            $phpContent .= "    </div>\n\n";
        }

        // Generate ONLY the post grid layout (remove the other list)
        $phpContent .= "    <div class=\"post-grid\">\n";
        $phpContent .= "        <?php foreach (\$category_posts as \$post): ?>\n";
        $phpContent .= "            <div class=\"post-card\">\n"; // Changed from <a> to <div>
        $phpContent .= "                <a href=\"<?php echo htmlspecialchars(\$post['url']); ?>\" class=\"post-card-link\">\n";
        $phpContent .= "                <div class=\"post-card-thumbnail\">\n";
        $phpContent .= "                    <?php if (isset(\$post['thumbnail']) && \$post['thumbnail']): ?>\n";
        $phpContent .= "                        <img src=\"<?php echo \$paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars(\$post['thumbnail']); ?>\" \n";
        $phpContent .= "                             alt=\"<?php echo htmlspecialchars(\$post['title']); ?>\" class=\"post-thumb-img\">\n";
        $phpContent .= "                    <?php else: ?>\n";
        $phpContent .= "                        <?php \$placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>\n";
        $phpContent .= "                        <?php \$random_placeholder = \$placeholder_images[array_rand(\$placeholder_images)]; ?>\n";
        $phpContent .= "                        <img src=\"<?php echo \$paths['base_path']; ?>img/thumbs/<?php echo \$random_placeholder; ?>\" \n";
        $phpContent .= "                             alt=\"<?php echo htmlspecialchars(\$post['title']); ?>\" class=\"post-thumb-img placeholder\">\n";
        $phpContent .= "                    <?php endif; ?>\n";
        $phpContent .= "                </div>\n";

        $phpContent .= "                <div class=\"post-card-content\">\n";
        $phpContent .= "                    <h3 class=\"post-card-title\"><?php echo htmlspecialchars(\$post['title']); ?></h3>\n";
        $phpContent .= "                    <p class=\"post-card-excerpt\"><?php echo htmlspecialchars(\$post['excerpt']); ?></p>\n";

        // Add metadata (author, date)
        $phpContent .= "                    <div class=\"post-card-meta\">\n";
        $phpContent .= "                        <?php if (isset(\$post['author']) && \$post['author']): ?>\n";
        $phpContent .= "                            <span class=\"post-author\">By <?php echo htmlspecialchars(\$post['author']); ?></span>\n";
        $phpContent .= "                        <?php endif; ?>\n";
        $phpContent .= "                        <?php if (isset(\$post['date']) && \$post['date']): ?>\n";
        $phpContent .= "                            <span class=\"post-date\"><?php echo is_string(\$post['date']) ? htmlspecialchars(\$post['date']) : ''; ?></span>\n";
        $phpContent .= "                        <?php endif; ?>\n";
        $phpContent .= "                    </div>\n";

        // Add tags
        $phpContent .= "                    <?php if (!empty(\$post['tags'])): ?>\n";
        $phpContent .= "                        <div class=\"post-card-tags\">\n";
        $phpContent .= "                            <?php foreach (\$post['tags'] as \$tag): ?>\n";
        $phpContent .= "                                <span class=\"tag\"><?php echo htmlspecialchars(\$tag); ?></span>\n";
        $phpContent .= "                            <?php endforeach; ?>\n";
        $phpContent .= "                        </div>\n";
        $phpContent .= "                    <?php endif; ?>\n";
        $phpContent .= "                </div>\n";
        $phpContent .= "                </a>\n"; // Close the anchor tag
        $phpContent .= "            </div>\n"; // Close the post-card div
        $phpContent .= "        <?php endforeach; ?>\n";
        $phpContent .= "    </div>\n";
        $phpContent .= "</div>\n";
        $phpContent .= "<?php\n";
        $phpContent .= "\$content = ob_get_clean();\n\n";

        $phpContent .= "// Include template\n";
        $phpContent .= "include \$paths['template_path'];\n";
        $phpContent .= "?>";

        return $phpContent;
    }

    private function getCategoryPosts($category) {
        $posts = [];
        $categoryDir = $this->contentDir . DIRECTORY_SEPARATOR . $category;

        if (!is_dir($categoryDir)) {
            return $posts;
        }

        $files = glob($categoryDir . '/*.md');

        foreach ($files as $file) {
            // Skip index.md as it's used for the category page itself
            if (basename($file) === 'index.md') {
                continue;
            }
            
            $content = file_get_contents($file);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $filename = basename($file, '.md');

                $posts[] = [
                    'title' => $frontmatter['title'] ?? $filename,
                    'author' => $frontmatter['author'] ?? null,
                    'date' => $frontmatter['date'] ?? null,
                    'excerpt' => $frontmatter['excerpt'] ?? $this->generateExcerpt($parsed['content']),
                    'url' => $filename . '.php',
                    'tags' => $frontmatter['tags'] ?? [],
                    'filename' => $filename,
                    'thumbnail' => $frontmatter['thumbnail'] ?? null
                ];
            }
        }

        // Sort posts by date (newest first) or by title if no date
        usort($posts, function($a, $b) {
            if ($a['date'] && $b['date']) {
                return strtotime($b['date']) - strtotime($a['date']);
            }
            return strcmp($a['title'], $b['title']);
        });

        return $posts;
    }

    public function getAllPosts() {
        $allPosts = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->contentDir)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $content = file_get_contents($file->getPathname());
                $parsed = $this->parseMarkdown($content);

                if ($parsed['frontmatter']) {
                    $frontmatter = $parsed['frontmatter'];
                    $relativePath = str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $relativePath = str_replace('\\', '/', $relativePath);
                    $filename = basename($file, '.md');

                    // Skip shorthand content
                    if ($this->isShorthandContent($file, $frontmatter)) {
                        continue;
                    }

                    $allPosts[] = [
                        'title' => $frontmatter['title'] ?? $filename,
                        'author' => $frontmatter['author'] ?? null,
                        'date' => $frontmatter['date'] ?? null,
                        'excerpt' => $frontmatter['excerpt'] ?? $this->generateExcerpt($parsed['content']),
                        'url' => str_replace('.md', '.php', $relativePath),
                        'tags' => $frontmatter['tags'] ?? [],
                        'category' => dirname($relativePath) === '.' ? 'general' : dirname($relativePath),
                        'filename' => $filename
                    ];
                }
            }
        }

        return $allPosts;
    }
}

// Run the builder
if (php_sapi_name() === 'cli') {
    $builder = new BlogBuilder();
    $builder->build();
} else {
    echo "This script should be run from the command line.";
}
?>






