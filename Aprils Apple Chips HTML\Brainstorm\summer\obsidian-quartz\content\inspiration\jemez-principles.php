<?php
// Auto-generated blog post
// Source: jemez-principles.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Jemez Principles for Democratic Organizing';
$meta_description = 'If we hope to achieve just choices that include all people in decision-making and assure that all people have an equitable share of the wealth and the work of this world, then we must work to build that.';
$meta_keywords = 'inspiration, resources, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Jemez Principles for Democratic Organizing',
  'author' => 'The Southwest Network for Environmental and Economic Justice, 1996',
  'tags' => 
  array (
    0 => 'inspiration',
    1 => 'resources',
  ),
  'excerpt' => 'If we hope to achieve just choices that include all people in decision-making and assure that all people have an equitable share of the wealth and the work of this world, then we must work to build that.',
  'date' => '1996-12-08',
  'categories' => 
  array (
    0 => 'Inspiration',
  ),
  'source_file' => 'content\\inspiration\\jemez-principles.md',
);

// Raw content
$post_content = '<h3>1. Be Inclusive</h3>

<p>If we hope to achieve just choices that include all people in decision-making and assure that all people have an equitable share of the wealth and the work of this world, then we must work to build that kind of inclusiveness into our own movement in order to develop alternative policies and institutions to the treaties and policies under neo-liberalism. This requires more than tokenism, it cannot be achieved without diversity at the planning table, in staffing, and in coordination. It may delay achievement of other important goals; it will require discussion, hard work, patience and advanced planning. It may involve conflict, but through this conflict, we can learn better ways of working together. It\'s about building alternative institutions, movement building, and not compromising in order to be accepted into the anti-globalization club.</p>

<h3>2. Emphasis on Bottom-Up Organizing</h3>

<p>To succeed, it is important to reach out into new constituencies, and to reach within all levels of the leadership and membership base of the organizations that are already involved in our networks. We must be continually building and strengthening a base which provides our credibility, our strategies, mobilizations, leadership development, and the energy for the work we must do daily.</p>

<h3>3. Let People Speak For Themselves</h3>

<p>We must be sure that relevant voices of people directly affected are heard. Ways must be provided for spokespersons to represent and be responsible to their affected constituencies. It is important for organizations to clarify their roles, who they represent, and to assure accountability within their structures.</p>

<h3>4. Work Together In Solidarity and Mutuality</h3>

<p>Groups working on similar issues with compatible visions should consciously act in solidarity, mutuality and support each other\'s work. In the long run, a more significant step is to incorporate the goals and values of other groups with your own work in order to build strong relationships. For instance, in the long run, it is more important that labor unions and community economic development projects include the issue of environmental sustainability in their own strategies, rather than just lending support to the environmental organizations. So communications, strategies, and resource sharing is critical to help us see our connections and build upon these.</p>

<h3>5. Build Just Relationships Among Ourselves</h3>

<p>We need to treat each other with justice and respect, both on an individual and organizational level, in this country and across borders. Defining and developing "just relationships" will be a process that won\'t happen overnight. It must include clarity about decision-making, sharing strategies, and resource distribution. There are clearly many skills necessary to succeed, and we need to determine the ways for those with different skills to coordinate and be accountable to one another.</p>

<h3>6. Commitment To Self Transformation</h3>

<p>As we change societies, we must change from operating on the mode of individualism to community centeredness. We must "walk our talk". We must be the values that we say we\'re struggling for—we must be justice, peace and community.</p>

<p>(Adopted on December 8, 1996, by the people of color participants during the pre-meeting portion of the "Working Group Meeting on Trade and Globalization" in Jemez, New Mexico hosted by The Southwest Network for Environmental and Economic Justice.)</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>