<?php
// Ensure we have path constants available
if (!isset($paths) && isset($config)) {
    require_once ($base_url ?? '') . 'path-helper.php';
    $paths = initPaths($config, __FILE__);
}
?>
<header>
    <div class="container">
        <a href="<?php echo ($paths['home_path'] ?? ($base_url ?? '') . 'index.html'); ?>">
            <?php echo htmlspecialchars($config['site']['name'] ?? 'A. A. Chips'); ?>
        </a>
        <nav class="nav">
            <img src="">
            <ul>
                <?php
                $navigation = $config['navigation'] ?? [
                    'Home' => 'index.html',
                    'About' => 'about.html',
                    'Advocacy' => 'advocacy.html',
                    'Personal' => 'personal.html',
                    'Gallery' => 'gallery.html'
                ];
                foreach ($navigation as $label => $url):
                ?>
                    <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . $url; ?>"><?php echo htmlspecialchars($label); ?></a></li>
                <?php endforeach; ?>
            </ul>
        </nav>
    </div>
</header>
