<?php
// Auto-generated blog post
// Source: Favorite Slaw Recipe.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Favorite Chicken Slaw Recipe';
$meta_description = '';
$meta_keywords = 'applechip<PERSON><PERSON>en, cook, aachips, videoscript, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Favorite Chicken Slaw Recipe',
  'date' => '2022-01-01',
  'tags' => 
  array (
    0 => 'applechipkitchen',
    1 => 'cook',
    2 => 'aachips',
    3 => 'videoscript',
  ),
  'author' => 'A. A. Chips',
  'source_file' => 'content\\kitchen\\Favorite Slaw Recipe.md',
);

// Raw content
$post_content = '<p>This is one of my favorite home recipes and comfort foods.</p>

<p>Why? I can eat it for days on end. It\'s cheap to make. It\'s healthy. And it doesn\'t take a lot of brain power.</p>

<p>Depending where you are and what time of year it is, cabbages are very cheap and under appreciated. With the right curing, they make an excellent salad green.</p>

<p>First you need to cure the cabbage. Cut the cabbage into bite sized pieces. What this looks like to different people will vary.</p>

<p>Then stuff those cabbage pieces in a container with a bunch of salt. More salt than you are comfortable adding. Don\'t worry, we will wash 98% of that salt off in a few hours. The starches get soaked out, and then you rinse through what remains. Some salt will get absorbed into the cabbage. I generally add something like a quarter cup of salt to a quart container stuffed with cabbage, and add a little bit of water to make sure the salt flows through.</p>

<p>When you rinse off the cabbage, it\'s more effective if you can put on food gloves (or not, don\'t let me tell you how to live your life) and literally squeeze out as much of the water content from the cabbage as possible.</p>

<p>There, you didn\'t even need to cook the cabbage. It\'s a great consistency.</p>

<p>Then you take your chicken. If you don\'t want to work with raw chicken, you can buy a rotisserie chicken, or a container of plain chicken. But what I like doing is just boiling the chicken. Some people will hate on me for that. But if you are making a salad with chicken. There is no seasoning needed. Just get it cooked. It doesn\'t need salt, because there will be plenty in the cabbage, and the chicken will help absorb some of the excess salt content.</p>

<p>With this, a pound of chicken can stretch a long way.</p>

<p>How you flavor this is up to you. There are a few different approaches. It doesn\'t take much. I like taking seaweed sheets and throwing them in a food processor and adding that. it\'s a great flavor loaded with nutrients like Selenium, which is really good for my thyroid issues.</p>

<p>You could also do something like, some dollops of W. Sauce, a glug of cider vinegar, and a couple heaps of Mayo.</p>

<p>With one cabbage and one pound of chicken, that\'s meals or snacking food for a couple of days for a single person. Or a meal for a family sometimes for under 15$.</p>

<p>It\'s best with some time to sit in refrigeration to marinate and develop its flavor.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>