<?php
// Auto-generated blog post
// Source: approach-consumption.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'My Approach to Consumption - A. A. Chips';
$meta_description = 'When asked about upcoming purchases, I struggle to answer—not because I can\'t afford things, but because buying isn\'t central to my lifestyle. Living below the poverty line for most of my adult life has shaped a deliberately minimal approach to consumption.';
$meta_keywords = 'journal, advocacy, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'My Approach to Consumption - A. A. Chips',
  'date' => '2025-02-24',
  'excerpt' => 'When asked about upcoming purchases, I struggle to answer—not because I can\'t afford things, but because buying isn\'t central to my lifestyle. Living below the poverty line for most of my adult life has shaped a deliberately minimal approach to consumption.',
  'author' => 'A. A. Chips',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'advocacy',
    2 => 'homeless',
  ),
  'source_file' => 'content\\journal\\approach-consumption.md',
);

// Raw content
$post_content = '<h2>My Approach to Consumption</h2>

<p>April Chips Early</p>

<p>When asked about upcoming purchases, I struggle to answer—not because I can\'t afford things, but because buying isn\'t central to my lifestyle. Living below the poverty line for most of my adult life has shaped a deliberately minimal approach to consumption.</p>

<h2>How I Meet Basic Needs Without Traditional Spending</h2>

<h3>Food</h3>

<ul><li>I cook most meals as a professional home cook</li>
<p><li>I participate in free community meal programs</li></p>
<p><li>My apple chip business uses surplus fruit that would otherwise go to waste</li></p>
<p><li>In earlier years, I rescued discarded food through dumpster diving</li></p>
<p><li>Now I work with nonprofits to legitimately redirect food waste</li></p>

<h3>Transportation</h3>

<p><li>I drive less than 10,000 miles yearly in a low-maintenance vehicle</li></p>
<p><li>Regular maintenance happens at a trusted local shop that doesn\'t invent problems</li></p>
<p><li>I focus on necessary repairs only—oil changes, occasional tire replacements, brake pads</li></p>

<h3>Household & Personal Items</h3>

<p><li>I help people clear out homes and storage units when they leave town</li></p>
<p><li>In exchange, I redistribute their unwanted belongings</li></p>
<p><li>Last month, this yielded clothes, books, art supplies, and even a PlayStation 3</li></p>
<p><li>Most items I receive are given away to others in need</li></p>

<h3>Financial Approach</h3>

<p><li>I maintain zero debt</li></p>
<p><li>My part-time job covers basic monthly expenses</li></p>
<p><li>My small business fills occasional financial gaps</li></p>
<p><li>I practice extreme intentionality with spending</li></p>

<h3>Leisure & Self-Care</h3>

<p><li>Annual camping trip replaces expensive vacations</li></p>
<p><li>I haven\'t flown or stayed in hotels in years</li></p>
<p><li>I manage health through nutrition, rest, and preventive practices</li></p>
<p><li>I\'ve adapted to minimal healthcare access despite having pre-existing conditions</li></ul></p>

<h2>My Essential Purchases</h2>

<p>Despite my minimal consumption habits, two unavoidable expenses remain central to my life:</p>

<p>1. <strong>Housing</strong>: Monthly rent ensures I have safe, stable shelter</p>
<p>2. <strong>Transportation</strong>: Occasional gas fill-ups keep my car running for essential travel</p>

<h2>The Bigger Picture</h2>

<p>This lifestyle isn\'t about deprivation—it\'s about intention. By stepping outside consumer culture, I\'ve found greater freedom and resilience. My approach might seem extreme to some, but it offers a perspective on what\'s truly necessary versus what\'s merely expected in our consumption-driven society.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>