<?php
// Auto-generated blog post
// Source: instacart-shopping.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'My Brief Stint as an Instacart Shopper..';
$meta_description = 'Like many others in May 2020, during the confusing and restrictive early COVID-19 lockdowns, I signed up to be an Instacart shopper. The welcome kit arrived with the essentials: a shopping card, lanyard, hand sanitizer, and a postcard. I, however, deemed it necessary to invest in my own safety beyond the provided single-use items, spending $25 on two handmade cloth masks with HEPA filter inserts to avoid depleting medical-grade supplies meant for healthcare workers.';
$meta_keywords = 'journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'My Brief Stint as an Instacart Shopper..',
  'date' => '2020-06-23',
  'author' => 'A. A. Chips',
  'tags' => 
  array (
    0 => 'journal',
  ),
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'excerpt' => 'Like many others in May 2020, during the confusing and restrictive early COVID-19 lockdowns, I signed up to be an Instacart shopper. The welcome kit arrived with the essentials: a shopping card, lanyard, hand sanitizer, and a postcard. I, however, deemed it necessary to invest in my own safety beyond the provided single-use items, spending $25 on two handmade cloth masks with HEPA filter inserts to avoid depleting medical-grade supplies meant for healthcare workers.',
  'source_file' => 'content\\journal\\instacart-shopping.md',
);

// Raw content
$post_content = '<p>Like many others in May 2020, during the confusing and restrictive early COVID-19 lockdowns, I signed up to be an Instacart shopper. The welcome kit arrived with the essentials: a shopping card, lanyard, hand sanitizer, and a postcard. I, however, deemed it necessary to invest in my own safety beyond the provided single-use items, spending $25 on two handmade cloth masks with HEPA filter inserts to avoid depleting medical-grade supplies meant for healthcare workers.</p>

<p>My experience consisted of roughly seven shopping trips. Six passed without major incident, but one in particular laid bare the fundamental flaws of the gig. Navigating a packed Aldi, I was tasked with locating every item on a stranger\'s digital list, photographing each for verification, all while constantly handling my phone in a crowded public space during a pandemic. When an item was out of stock, immediate real-time communication with the customer for a replacement was required, adding further time and potential exposure.</p>

<p>Instacart meticulously tracked my "average time per item," which hovered around 70 seconds. This meant a $10 batch containing 20 items consumed nearly 30 minutes of just the shopping itself. Factor in the 15-minute wait in the checkout line, paying with the Instacart card, and the subsequent unrealistic ten-minute delivery window, and the inherent pressures became evident. As someone with health disabilities and a naturally slower pace, deliveries often took me closer to twenty minutes. The app offered zero flexibility, creating constant pressure to meet the demands of customers expecting "instant" service. The process continued with loading my own car, utilizing my personal plastic totes, and often the customers would unload their own groceries, which at least allowed for some control over sanitization.</p>

<p>The delivery routes frequently led me to isolated, backroad locations, amplifying my personal safety concerns as a gay individual. I couldn\'t help but consider the even greater dangers a car breakdown in such areas would pose for Black drivers. Alcohol deliveries introduced the added responsibility of ID verification. A seemingly minor issue, a customer\'s refusal to show ID would not only prevent the delivery but also lock me out of future gigs, effectively ending my workday. Considering the drive back to the store, a meager $10 batch often stretched into an hour and a half, leaving me with approximately five dollars an hour after covering gas expenses.</p>

<p>Disturbingly, Instacart never once inquired about my pre-existing health conditions. I live with autoimmune lung problems, and in hot weather, I am susceptible to delirium, brain inflammation, and pain – all of which slowed down my shopping and increased my risk of driving accidents. The bright fluorescent lights and constant noise in the stores were overwhelming and draining. Contracting COVID while working was a very real possibility, one that could have led to expensive hospitalization and potential intubation. However, due to convenient legal loopholes, Instacart classifies shoppers as independent contractors, allowing them to easily dismiss any concerns, even hospitalization resulting directly from their work. Seeking legal recourse is practically impossible. My physical limitations during hot weather significantly hampered my ability to work.</p>

<p>The <a href="../content/https:/watch-v-eymxipzjq2m.php" class="internal-link">story shared by the YouTuber badempanada</a> about a gig bike delivery worker hospitalized after a hit-and-run deeply resonated with my fears. Furthermore, my overactive bladder necessitated frequent bathroom breaks, a risky endeavor in public during the pandemic. This forced me into uncomfortable choices: driving home between gigs, finding secluded outdoor spots, or, in desperation, resorting to unsanitary options like using a cup in my car. Naturally, Instacart wouldn\'t cover the cost of adult diapers.</p>

<p>The entire structure felt exploitative, an abusive situation designed to extract labor from gig workers without providing adequate support or protection. These individuals deserve the right to unionize and collectively bargain for fair treatment. Instead, Instacart reportedly retaliates by firing those who attempt to organize. So, with a resounding "Fuck you, Instacart," I quit.</p>

<p>If you find yourself in need of grocery shopping assistance, I strongly recommend seeking out a trustworthy individual to establish a direct client-provider relationship with and ensuring they are compensated fairly with generous tips.</p>

<p>For those who depend on Instacart for their livelihood, my heart goes out to you. Please explore avenues for offering your services independently, free from the constraints of this exploitative platform.</p>

<p>I was fortunate enough not to rely on those meager wages for survival. Instead, I volunteered for months assembling and delivering free food boxes to high-risk households, opting for cooler nighttime deliveries with the recipients\' consent. This allowed me to work at my own pace, unburdened by Instacart\'s unrealistic and dehumanizing demands.</p>

<p>Meaningful change will only come when gig workers possess collective bargaining power. Grocery stores that offer these services should implement on-demand, on-site shopping for those unable or unwilling to wear masks (like families with young children), alongside options for car delivery or employing separate drivers. Slowing down the relentlessly rushed pace and ensuring workers receive a fair wage – well above $15 an hour – is absolutely crucial. Furthermore, considering 24/7 operation for high-volume stores could alleviate congestion and facilitate late-night delivery services, better serving both customers and workers.</p>


<iframe width="560" height="315" src="https://www.youtube.com/embed/EYMXiPzjQ2M?si=WDNDNbOXooHJmQiI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>