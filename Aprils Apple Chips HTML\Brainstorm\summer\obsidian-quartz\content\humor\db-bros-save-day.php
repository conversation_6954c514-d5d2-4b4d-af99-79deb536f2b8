<?php
// Auto-generated blog post
// Source: db-bros-save-day.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Diarrhea Brothers Save the Day';
$meta_description = '<PERSON>, <PERSON> and <PERSON> are delivery drivers for their family business. <PERSON>, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by <PERSON>';
$meta_keywords = 'humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Diarrhea Brothers Save the Day',
  'author' => 'Joel Haver',
  'excerpt' => 'John, Jonathan and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by Joel Haver',
  'tags' => 
  array (
    0 => 'humor',
  ),
  'source_file' => 'content\\humor\\db-bros-save-day.md',
);

// Raw content
$post_content = '<p>This is by far the best stupidest film I have ever watched.</p>
<iframe width="560" height="315" src="https://www.youtube.com/embed/h3UA8yfKRFY?si=rmyNnaQmxwRxOmqm" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h1>The Diarrhea Brothers Save The Day</h1>

<p><a href="https://www.youtube.com/@Joel-Haver" class="external-link"><img src="https://yt3.ggpht.com/ytc/AIdro_nTpopaBwC4gK7AJO5OUcKLuVLY8Gy16XZ6l7Z3rj_Kl60=s48-c-k-c0x00ffffff-no-rj" alt=""></a></p>

<p><a href="https://www.youtube.com/channel/UCVIFCOJwv3emlVmBbPCZrvw" class="external-link">Joel Haver</a></p>

<p>2.06M subscribers</p>
<p>120,914 views Nov 23, 2024</p>

<p>John, Jonathan and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by Joel Haver, <a href="https://www.youtube.com/@TrentLenkarski" class="external-link">‪@TrentLenkarski‬</a>, <a href="https://www.youtube.com/@alexmartens" class="external-link">‪@alexmartens‬</a> and <a href="https://www.youtube.com/@-masoncarter" class="external-link">‪@-masoncarter‬</a> Featuring <a href="https://www.youtube.com/@Sethward" class="external-link">‪@Sethward‬</a> <a href="https://www.youtube.com/@PauletteJones" class="external-link">‪@PauletteJones‬</a> <a href="https://www.youtube.com/@larsmidthun6287" class="external-link">‪@larsmidthun6287‬</a> <a href="https://www.youtube.com/@brittneyraeallday" class="external-link">‪@brittneyraeallday‬</a> Paulina Gregory <a href="https://www.youtube.com/@realsydsmith" class="external-link">‪@realsydsmith‬</a> <a href="https://www.youtube.com/@BennyBall" class="external-link">‪@BennyBall‬</a> <a href="https://www.youtube.com/@RyanTheLeader" class="external-link">‪@RyanTheLeader‬</a> <a href="https://www.youtube.com/@magentasquash" class="external-link">‪@magentasquash‬</a> <a href="https://www.youtube.com/@Yehslacks" class="external-link">‪@Yehslacks‬</a> <a href="https://www.youtube.com/@SvenJohnsonBestYoutuber" class="external-link">‪@SvenJohnsonBestYoutuber‬</a> <a href="https://www.youtube.com/@gustoonz" class="external-link">‪@gustoonz‬</a> <a href="https://www.youtube.com/@Daxflame" class="external-link">‪@Daxflame‬</a> Tom Goulet Firas Catler Sharhar Hillel Walt Lusk StaggerLee Cole Orignal Score by <a href="https://www.youtube.com/@trentzulkiewicz5700" class="external-link">‪@trentzulkiewicz5700‬</a> and <a href="https://www.youtube.com/@droodle" class="external-link">‪@droodle‬</a> Additional Music by <a href="https://www.youtube.com/@kevinkevinkevinkevinkevin" class="external-link">‪@kevinkevinkevinkevinkevin‬</a> <a href="https://www.youtube.com/@camraleigh" class="external-link">‪@camraleigh‬</a> and <a href="https://www.youtube.com/@MyKeyTheArtist" class="external-link">‪@MyKeyTheArtist‬</a> Special Effects by <a href="https://www.youtube.com/@tomgoulet" class="external-link">‪@tomgoulet‬</a> Special Effects Makeup by Jenn Osborne and Leigh Mader Other Channels - Joel Talks About Movies -    <a href="https://www.youtube.com/channel/UCTy7s81ii4fe4xNMGFNwYxA" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/yt_favicon_ringo2.png" alt=""> / @joeltalksaboutmovies</a>   goodlongpee -    <a href="https://www.youtube.com/c/goodlongpee" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/yt_favicon_ringo2.png" alt=""> / goodlongpee</a>   Support - Patreon:   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbkFQQVI3ZXRpS0kzQVd6NHpDLVBPQ3hENXlYd3xBQ3Jtc0tuTGJsZFNTSjI1ZFRSWm4wajBlOHhGWTlibzhvWkVCQmhnNF9uRE51Wk1ZLWJYNHUyRHF2LWFhb1d0MG1XSXNFMXRKZ1E0MFcxZkdpWTlHZmxoRTRzUlAzRktBcG4zV0FSaUdLY2FSQVVhcTZSdzRIRQ&q=https%3A%2F%2Fpatreon.com%2Fjoelhaver&v=h3UA8yfKRFY" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/patreon_1x_v2.png" alt=""> / joelhaver</a>   Paypal: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbHAxOWxraXFxVGxXcHFvWXM3ZVRoVG1sX2tXQXxBQ3Jtc0trZ3Zod2t1ZTJwNDNQaXZLcUNWLVBFN096RklaWnhIc2hWVUlzTE5UUkZvNHRtMWhCdlBJaDR4VHRUTmp1WlY0RmNVTnF0dXlybkY4NE1OZmVid2FFZXdfMU9qNnRhMTNQTm9saUowVFNvb0cyUU1nWQ&q=https%3A%2F%2Fbit.ly%2F2ZI7uff&v=h3UA8yfKRFY" class="external-link">https://bit.ly/2ZI7uff</a> Merch - <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbGNoeXBaSjI1OFRSSFRnZk8tdTh4aVFVT3ppZ3xBQ3Jtc0trbzQwTW1fR0taMmFIeVZOdVJXNTNqekF1Yi1QbVFHaERqM08wV2hMeHdfdXdHWnMwM3BBQU1HcWhnY0taejFHNHVwSEJEZThhaXVXYV9NN0xhWGNGeF9LZms5bWk4QXlhTHBlemtBN2wycUd0OXo1dw&q=https%3A%2F%2Fjoelstuff.store%2F&v=h3UA8yfKRFY" class="external-link">https://joelstuff.store</a> Social - Instagram:   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbE9xdmNPaFp1NFJ4MnVweE53R1lGM1ZMT3ljZ3xBQ3Jtc0tuVTktMkdTcGk1TXNoeVlIcmIxaXBsd01RTDRaOHlJVHBYYXJBV2FWNGY3WnJ2VmxwdWwwMGNDOXpVSmlRY3RpeUZvdXNCdGVfdFBXa0NqNFRNYmRQSVhaaXNSZTYxN0IxNWNfVGtqSk0tS1E0U3gtYw&q=https%3A%2F%2Fwww.instagram.com%2Fjoelhaver&v=h3UA8yfKRFY" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/instagram_1x.png" alt=""> / joelhaver</a>   Twitter:   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbUM5cUZHZmtxQzJobnVscFpiTVkzNUZXajRpd3xBQ3Jtc0ttTVlFUUZuMmc3dngyV1RIWHFYOVQ0ZXRtbW9VN044UG0yeENFMWlybkhCbWRrM2xDUFc2RWFQSXktNjgwMElWVHp5VXhCcjVoN05qbXBJbml5LW9iNVVaV1RXdVdIVUxhRFk1V0RHSXQ2dmtJdHBhRQ&q=https%3A%2F%2Ftwitter.com%2Fjoelhaver&v=h3UA8yfKRFY" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/twitter_1x_v2.png" alt=""> / joelhaver</a>   Drawings:   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbUxLX01HLXVkLW9VYXl5ejhuUVNMbFpkdHFQQXxBQ3Jtc0tsT0ZMTDg0R0hLb3dsX3JxeHIxVlQ3Mi1ySF9ORHg3V3JGbEIzX0tWbjBKdmpTZjNwYkZqX3haZ1NvRUl2NEdBNk1tdmVoRC1DZ3picmEzMG1BQ1BweHA0V0xkbC1FeXluV2h0NXBhb2xzRTBKckVraw&q=https%3A%2F%2Fwww.instagram.com%2Fjoeldrawsandwriteshaver&v=h3UA8yfKRFY" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/instagram_1x.png" alt=""> / joeldrawsandwriteshaver</a>   Letterboxd: <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqblE0VWpqNHYzSlo3dUtMWGlmMm56ZXBOUGZXd3xBQ3Jtc0trTjlGc3NPOGh0el9HTVA2QzY2NkhPM3lGLURSWUlpXzl5bnFFalBPYnotNk5Yc0J2dGNjRjVfWlFUQUFpU0gyUU5Zb0xsMGFqaEZoc29La3lKS19SbGozb1RwSzB4QXJUUElMODlfbHFYVDBZQTZ0Yw&q=https%3A%2F%2Fletterboxd.com%2Fjoelhaver&v=h3UA8yfKRFY" class="external-link">https://letterboxd.com/joelhaver</a></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>