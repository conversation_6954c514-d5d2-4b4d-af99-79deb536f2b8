<?php
/**
 * Site Configuration
 * Central configuration for A. A. Chips' Blog
 */

return [
    // Site Information
    'site' => [
        'name' => 'A. A. Chips',
        'title' => 'A. A. Chips\' Blog',
        'tagline' => 'Personal stories, advocacy work, and reflections',
        'description' => 'Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.',
        'author' => 'A. A. Chips',
        'url' => '', // Base URL - leave empty for relative paths
        'language' => 'en',
        'charset' => 'UTF-8'
    ],

    // SEO & Meta
    'meta' => [
        'keywords' => 'A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life',
        'robots' => 'index, follow',
        'viewport' => 'width=device-width, initial-scale=1.0'
    ],

    // Social Media & External Services
    'social' => [
        'kofi' => 'https://www.ko-fi.com/aachips',
        'chatway_enabled' => true,
        'clarity_enabled' => true
    ],

    // Navigation
    'navigation' => [
        'Home' => 'index.php',
        'About' => 'about.php',
        'Advocacy' => 'advocacy.php',
        'Personal' => 'personal.php',
        'Gallery' => 'gallery.php'
    ],

    // Categories
    'categories' => [
        'Street Advocacy' => 'content/street/index.php',
        'Apple Chip Kitchen' => 'content/kitchen/index.php',
        'Alienation' => 'content/alienation/index.php',
        'Climate' => 'content/climate/index.php',
        'Humor' => 'content/humor/index.php',
        'Inspiration' => 'content/inspiration/index.php',
        'Journal' => 'content/journal/index.php',
        'Personal Stories' => 'content/personal/index.php',
        'Writings' => 'content/writings/index.php'
    ],

    // Build Settings
    'build' => [
        'generate_php' => true, // Generate PHP files instead of static HTML
        'include_post_meta' => true,
        'include_related_posts' => true,
        'excerpt_length' => 150,
        'posts_per_page' => 10
    ],

    // Paths - Enhanced with dynamic path resolution
    'paths' => [
        'css' => 'css/',
        'js' => 'js/',
        'images' => 'img/',
        'includes' => 'includes/',
        'templates' => 'templates/',
        'content' => 'content/',
        'data' => 'data/'
    ],

    // Path Constants - Dynamically calculated based on file location
    'path_constants' => [
        'enable_dynamic_paths' => true,
        'site_root' => '', // Will be calculated dynamically
        'content_base' => 'content/', // Base content directory
        'prevent_content_loops' => true, // Prevent /content/content/ loops
        'force_relative_paths' => true, // Use relative paths instead of absolute
    ],

    // URL Structure
    'url_structure' => [
        'base_url' => '', // Leave empty for relative URLs
        'content_url_prefix' => '', // No prefix to avoid /content/content/ loops
        'clean_urls' => false, // Set to true if using URL rewriting
        'file_extension' => '.php' // Default file extension for generated files
    ]
];
