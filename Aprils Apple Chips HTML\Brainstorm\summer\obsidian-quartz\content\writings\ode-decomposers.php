<?php
// Auto-generated blog post
// Source: ode-decomposers.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Ode to the Decomposers - Why We Should Aspire to Be More Like Mushrooms';
$meta_description = 'Exploring the metaphor of mushrooms and decomposition in relation to societal and environmental issues.';
$meta_keywords = 'inspiration, writings, mushrooms, decomposition, waste, regeneration, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Ode to the Decomposers - Why We Should Aspire to Be More Like Mushrooms',
  'author' => 'A. A. Chips',
  'date' => '2025-06-06',
  'tags' => 
  array (
    0 => 'inspiration',
    1 => 'writings',
    2 => 'mushrooms',
    3 => 'decomposition',
    4 => 'waste',
    5 => 'regeneration',
  ),
  'excerpt' => 'Exploring the metaphor of mushrooms and decomposition in relation to societal and environmental issues.',
  'categories' => 
  array (
    0 => 'writings',
  ),
  'source_file' => 'content\\writings\\ode-decomposers.md',
);

// Raw content
$post_content = '<p>Why should we buy into the relentless cycle of economic production and consumption? The mountains of waste generated by this pursuit feel increasingly overwhelming. Perhaps instead of striving to be the next titan of industry, we should look to a different kind of life form for inspiration: the humble mushroom.</p>

<p>Imagine a world with more "mushroom-like" people. In our current system, if you\'re not engaged in traditional employment, diligently contributing to the machinery of production and consumption, you\'re often labeled as doing "nothing." If you aren\'t generating value for bankers, your efforts are deemed insignificant. But who truly cares about padding the pockets of bankers? Isn\'t there a more profound kind of worth to be created?</p>

<p>Consider the magic of mushrooms. They take the refuse, the decay, the literal shit of the world, and transform it into something new, something vital, something even beautiful. They are the ultimate recyclers, the unsung heroes of the ecosystem. They enrich the soil, facilitating new life from what was once considered waste.</p>

<p>While you might imagine me indulging in some mind-altering mushroom tea, my current focus is on a far more grounded endeavor: crafting a large mushroom pizza with my little one. And as we prepare this simple meal, I can\'t help but ponder why more of us can\'t embrace the "mushroom mindset."</p>

<p>What if we shifted our focus from endless accumulation to meaningful transformation? What if we valued the act of decomposition and regeneration as much as we celebrate production? Perhaps then, we could begin to address the mountains of societal and environmental waste, turning our collective "shit" into something truly wonderful and beautiful. Let\'s cultivate a world where being like a mushroom isn\'t seen as doing nothing, but as doing something profoundly important.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>