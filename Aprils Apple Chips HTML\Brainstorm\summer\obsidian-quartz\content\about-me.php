<?php
// Auto-generated blog post
// Source: about-me.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'About Me - A. A. Chips
"Date:": 5/22/2025';
$meta_description = 'Here are some blurbs about me.';
$meta_keywords = '';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Ash', 'url' => PathHelper::getCategoryIndexPath('ash'), 'excerpt' => 'Browse all Ash posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'About Me - A. A. Chips
"Date:": 5/22/2025',
  'excerpt' => 'Here are some blurbs about me.',
  'author' => 'A. A. Chips',
  'source_file' => 'content\\about-me.md',
);

// Raw content
$post_content = '<p>Here are some blurbs about me:</p>
<ul><li>I\'m a terrible cook. Everyone hates my food. I still persevere. Check out food and private chefing content. <a href="content/apple-chip-kitchen.php" class="internal-link">Apple Chip Kitchen</a></li>
<p><li>I am the inventor of Chiptocurrency, the only money that you ~~can~~ should eat as a snack. This also includes <a href="../obsidian-quartz/content/index.php?action=post&post=chips" class="internal-link">Digital Chips</a> like browser cookies, but healthier..</li></p>
<p><li>I used to be homeless mainly between the time of 2014 and 2019. This looked like walking across America in the beginning, to living between my car and peoples couches, to freaking out on my parent\'s behavior, and running away with my car seeking refuge to a new city and state. If you want to learn more about why I made that decision, what my life has been like, or about the street advocacy that I do, then I welcome you to follow the links.</li></p>
<p><li>I advocate for reforms and structural changes in mental health, social work, peer support, housing for all, and our food system. I have a lot of writings on here.</li></p>
<p><li>I\'ve been going back to school the past three years and learning how to code websites and applications, and have some really cool projects I\'d love for you to check out. </li></p>
<p><li>I\'m not on good terms with my biological family. This project is not about them, but they have played a role in my story and I will talk smack or offer flowers when and if I feel like it on here. If you know me from my former life, or if you are related to me by blood, first off, \'Hello!\' Make yourself at home, just respect that I want nothing to do with my parents or sibling. </li></p>
<p><li>I regularly throw car batteries into the ocean. Here\'s a <a href="content/eco-sattva-vows.php" class="internal-link">bunch of content about sustainability, climate, and walking across America</a>. </li></p>
<p><li>To learn Spanish the Jewish way click here. Para ambezar Espanyol como Judiya marqe akí.</li></ul></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>